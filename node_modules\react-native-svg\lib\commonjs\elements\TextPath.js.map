{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_extractTransform", "_interopRequireDefault", "_extractProps", "_extractText", "_util", "_Shape", "_TSpan", "_TextPathNativeComponent", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "TextPath", "<PERSON><PERSON><PERSON>", "displayName", "setNativeProps", "props", "matrix", "extractTransform", "assign", "pickNotNil", "extractText", "root", "render", "children", "xlinkHref", "href", "startOffset", "method", "spacing", "side", "alignmentBaseline", "midLine", "prop", "matched", "match", "idPattern", "withoutXY", "ref", "refMethod", "createElement", "console", "warn", "exports"], "sourceRoot": "../../../src", "sources": ["elements/TextPath.tsx"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,aAAA,GAAAH,OAAA;AAWA,IAAAI,YAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,MAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,wBAAA,GAAAN,sBAAA,CAAAF,OAAA;AAA8D,SAAAE,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAV,wBAAAU,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAa/C,MAAMW,QAAQ,SAASC,cAAK,CAAgB;EACzD,OAAOC,WAAW,GAAG,UAAU;EAE/BC,cAAc,GACZC,KAGkB,IACf;IACH,MAAMC,MAAM,GAAG,CAACD,KAAK,CAACC,MAAM,IAAI,IAAAC,yBAAgB,EAACF,KAAK,CAAC;IACvD,IAAIC,MAAM,EAAE;MACVD,KAAK,CAACC,MAAM,GAAGA,MAAM;IACvB;IACAb,MAAM,CAACe,MAAM,CAACH,KAAK,EAAE,IAAAI,gBAAU,EAAC,IAAAC,oBAAW,EAACL,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACM,IAAI,IAAI,IAAI,CAACA,IAAI,CAACP,cAAc,CAACC,KAAK,CAAC;EAC9C,CAAC;EAEDO,MAAMA,CAAA,EAAG;IACP,MAAM;MACJC,QAAQ;MACRC,SAAS;MACTC,IAAI,GAAGD,SAAS;MAChBE,WAAW,GAAG,CAAC;MACfC,MAAM;MACNC,OAAO;MACPC,IAAI;MACJC,iBAAiB;MACjBC,OAAO;MACP,GAAGC;IACL,CAAC,GAAG,IAAI,CAACjB,KAAK;IACd,MAAMkB,OAAO,GAAGR,IAAI,IAAIA,IAAI,CAACS,KAAK,CAACC,eAAS,CAAC;IAC7C,MAAMD,KAAK,GAAGD,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IACnC,IAAIC,KAAK,EAAE;MACT,MAAMnB,KAAK,GAAG,IAAAqB,uBAAS,EAAC,IAAI,EAAEJ,IAAI,CAAC;MACnC7B,MAAM,CAACe,MAAM,CACXH,KAAK,EACL,IAAAK,oBAAW,EACT;QACEG;MACF,CAAC,EACD,IACF,CAAC,EACD;QACEE,IAAI,EAAES,KAAK;QACXR,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,IAAI;QACJC,iBAAiB;QACjBC;MACF,CACF,CAAC;MACDhB,KAAK,CAACsB,GAAG,GAAG,IAAI,CAACC,SAAiD;MAClE,oBAAO1D,KAAA,CAAA2D,aAAA,CAACjD,wBAAA,CAAAG,OAAa,EAAKsB,KAAQ,CAAC;IACrC;IAEAyB,OAAO,CAACC,IAAI,CACV,oFAAoF,GAClFhB,IAAI,GACJ,GACJ,CAAC;IACD,oBACE7C,KAAA,CAAA2D,aAAA,CAAClD,MAAA,CAAAI,OAAK;MAAC4C,GAAG,EAAE,IAAI,CAACC;IAAkD,GAChEf,QACI,CAAC;EAEZ;AACF;AAACmB,OAAA,CAAAjD,OAAA,GAAAkB,QAAA", "ignoreList": []}