// Investment Service - Handle investment operations

import { Investment } from '../types';
import databaseService from '../database';

class InvestmentService {
  async getAllInvestments(): Promise<Investment[]> {
    try {
      const rows = await databaseService.executeQuery(
        'SELECT * FROM investments ORDER BY created_at DESC'
      );

      return rows.map(row => ({
        id: row.id,
        name: row.name,
        type: row.type,
        symbol: row.symbol,
        quantity: row.quantity || 0,
        purchasePrice: row.purchase_price || 0,
        currentPrice: row.current_price || 0,
        purchaseDate: new Date(row.purchase_date),
        maturityDate: row.maturity_date ? new Date(row.maturity_date) : undefined,
        interestRate: row.interest_rate,
        totalInvested: row.total_invested || 0,
        currentValue: row.current_value || 0,
        gainLoss: row.gain_loss || 0,
        gainLossPercentage: row.gain_loss_percentage || 0,
        isActive: Boolean(row.is_active),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
      }));
    } catch (error) {
      console.error('Error getting investments:', error);
      throw error;
    }
  }

  async getInvestmentById(id: string): Promise<Investment | null> {
    try {
      const rows = await databaseService.executeQuery(
        'SELECT * FROM investments WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      const row = rows[0];
      return {
        id: row.id,
        name: row.name,
        type: row.type,
        symbol: row.symbol,
        quantity: row.quantity || 0,
        purchasePrice: row.purchase_price || 0,
        currentPrice: row.current_price || 0,
        purchaseDate: new Date(row.purchase_date),
        maturityDate: row.maturity_date ? new Date(row.maturity_date) : undefined,
        interestRate: row.interest_rate,
        totalInvested: row.total_invested || 0,
        currentValue: row.current_value || 0,
        gainLoss: row.gain_loss || 0,
        gainLossPercentage: row.gain_loss_percentage || 0,
        isActive: Boolean(row.is_active),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
      };
    } catch (error) {
      console.error('Error getting investment by id:', error);
      throw error;
    }
  }

  async createInvestment(investment: Omit<Investment, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const id = Date.now().toString();
      const now = new Date().toISOString();

      // Calculate derived values
      const quantity = investment.quantity || 0;
      const purchasePrice = investment.purchasePrice || 0;
      const currentPrice = investment.currentPrice || 0;
      const totalInvested = quantity * purchasePrice;
      const currentValue = quantity * currentPrice;
      const gainLoss = currentValue - totalInvested;
      const gainLossPercentage = totalInvested > 0 ? (gainLoss / totalInvested) * 100 : 0;

      await databaseService.executeQuery(
        `INSERT INTO investments (
          id, name, type, symbol, quantity, purchase_price, current_price,
          purchase_date, maturity_date, interest_rate, total_invested,
          current_value, gain_loss, gain_loss_percentage, is_active,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id,
          investment.name,
          investment.type,
          investment.symbol || null,
          quantity,
          purchasePrice,
          currentPrice,
          investment.purchaseDate.toISOString(),
          investment.maturityDate ? investment.maturityDate.toISOString() : null,
          investment.interestRate || null,
          totalInvested,
          currentValue,
          gainLoss,
          gainLossPercentage,
          investment.isActive ? 1 : 0,
          now,
          now,
        ]
      );

      return id;
    } catch (error) {
      console.error('Error creating investment:', error);
      throw error;
    }
  }

  async updateInvestment(id: string, updates: Partial<Investment>): Promise<void> {
    try {
      const now = new Date().toISOString();

      // Get current investment data to calculate derived values
      const current = await this.getInvestmentById(id);
      if (!current) {
        throw new Error('Investment not found');
      }

      // Merge updates with current data
      const merged = { ...current, ...updates };

      // Calculate derived values
      const quantity = merged.quantity || 0;
      const purchasePrice = merged.purchasePrice || 0;
      const currentPrice = merged.currentPrice || 0;
      const totalInvested = quantity * purchasePrice;
      const currentValue = quantity * currentPrice;
      const gainLoss = currentValue - totalInvested;
      const gainLossPercentage = totalInvested > 0 ? (gainLoss / totalInvested) * 100 : 0;

      // Add calculated fields to updates
      const updatesWithCalculated = {
        ...updates,
        totalInvested,
        currentValue,
        gainLoss,
        gainLossPercentage,
        updatedAt: now
      };

      // Map camelCase to snake_case for database columns
      const columnMap: Record<string, string> = {
        purchasePrice: 'purchase_price',
        currentPrice: 'current_price',
        purchaseDate: 'purchase_date',
        maturityDate: 'maturity_date',
        interestRate: 'interest_rate',
        totalInvested: 'total_invested',
        currentValue: 'current_value',
        gainLoss: 'gain_loss',
        gainLossPercentage: 'gain_loss_percentage',
        isActive: 'is_active',
        createdAt: 'created_at',
        updatedAt: 'updated_at'
      };

      const setClause = Object.keys(updatesWithCalculated)
        .filter(key => key !== 'id' && key !== 'createdAt')
        .map(key => `${columnMap[key] || key} = ?`)
        .join(', ');

      const values = Object.keys(updatesWithCalculated)
        .filter(key => key !== 'id' && key !== 'createdAt')
        .map(key => {
          const value = updatesWithCalculated[key as keyof Investment];
          if (key === 'purchaseDate' || key === 'maturityDate') {
            return value instanceof Date ? value.toISOString() : value;
          }
          if (key === 'isActive') {
            return value ? 1 : 0;
          }
          return value;
        });

      await databaseService.executeQuery(
        `UPDATE investments SET ${setClause}, updated_at = ? WHERE id = ?`,
        [...values, now, id]
      );
    } catch (error) {
      console.error('Error updating investment:', error);
      throw error;
    }
  }

  async deleteInvestment(id: string): Promise<void> {
    try {
      await databaseService.executeQuery(
        'DELETE FROM investments WHERE id = ?',
        [id]
      );
    } catch (error) {
      console.error('Error deleting investment:', error);
      throw error;
    }
  }

  async updateInvestmentPrice(id: string, currentPrice: number): Promise<void> {
    try {
      await this.updateInvestment(id, { currentPrice });
    } catch (error) {
      console.error('Error updating investment price:', error);
      throw error;
    }
  }

  async getInvestmentsByType(type: string): Promise<Investment[]> {
    try {
      const rows = await databaseService.executeQuery(
        'SELECT * FROM investments WHERE type = ? ORDER BY created_at DESC',
        [type]
      );

      return rows.map(row => ({
        id: row.id,
        name: row.name,
        type: row.type,
        symbol: row.symbol,
        quantity: row.quantity || 0,
        purchasePrice: row.purchase_price || 0,
        currentPrice: row.current_price || 0,
        purchaseDate: new Date(row.purchase_date),
        maturityDate: row.maturity_date ? new Date(row.maturity_date) : undefined,
        interestRate: row.interest_rate,
        totalInvested: row.total_invested || 0,
        currentValue: row.current_value || 0,
        gainLoss: row.gain_loss || 0,
        gainLossPercentage: row.gain_loss_percentage || 0,
        isActive: Boolean(row.is_active),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
      }));
    } catch (error) {
      console.error('Error getting investments by type:', error);
      throw error;
    }
  }

  async clearAllInvestments(): Promise<void> {
    try {
      await databaseService.executeQuery('DELETE FROM investments');
    } catch (error) {
      console.error('Error clearing investments:', error);
      throw error;
    }
  }

  // Calculate total portfolio value
  calculatePortfolioValue(investments: Investment[]): number {
    return investments.reduce((total, investment) => {
      const quantity = investment.quantity || 0;
      const currentPrice = investment.currentPrice || 0;
      return total + (quantity * currentPrice);
    }, 0);
  }

  // Calculate total investment cost
  calculateInvestmentCost(investments: Investment[]): number {
    return investments.reduce((total, investment) => {
      const quantity = investment.quantity || 0;
      const purchasePrice = investment.purchasePrice || 0;
      return total + (quantity * purchasePrice);
    }, 0);
  }

  // Calculate profit/loss
  calculateProfitLoss(investments: Investment[]): number {
    const currentValue = this.calculatePortfolioValue(investments);
    const investmentCost = this.calculateInvestmentCost(investments);
    return currentValue - investmentCost;
  }

  // Calculate profit/loss percentage
  calculateProfitLossPercentage(investments: Investment[]): number {
    const investmentCost = this.calculateInvestmentCost(investments);
    if (investmentCost === 0) return 0;
    
    const profitLoss = this.calculateProfitLoss(investments);
    return (profitLoss / investmentCost) * 100;
  }
}

export const investmentService = new InvestmentService();
export default investmentService;
