import Shape from './elements/Shape';
import { camelCase, parse, SvgAst, SvgFromUri, SvgFromXml, SvgUri, SvgXml } from './xml';
import { fetchText } from './utils/fetchData';
import { RNSVGCircle, RNSVGClipPath, RNSVGDefs, RNSVGEllipse, RNSVGFeColorMatrix, RNSVGFeComposite, RNSVGFeGaussianBlur, RNSVGFeMerge, RNSVGFeOffset, RNSVGFilter, RNSVGForeignObject, RNSVGGroup, RNSVGImage, RNSVGLine, RNSVGLinearGradient, RNSVGMarker, RNSVGMask, RNSVGPath, RNSVGPattern, RNSVGRadialGradient, RNSVGRect, RNSVGSvgAndroid, RNSVGSvgIOS, RNSVGSymbol, RNSVGText, RNSVGTextPath, RNSVGTSpan, RNSVGUse } from './fabric';
export { inlineStyles, loadLocalRawResource, LocalSvg, SvgCss, SvgCssUri, SvgWithCss, SvgWithCssUri, WithLocalSvg } from './deprecated';
export * from './lib/extract/types';
export { camelCase, fetchText, parse, RNSVGCircle, RNSVGClipPath, RNSVGDefs, RNSVGEllipse, RNSVGFeColorMatrix, RNSVGFeComposite, RNSVGFeGaussianBlur, RNSVGFeMerge, RNSVGFeOffset, RNSVGFilter, RNSVGForeignObject, RNSVGGroup, RNSVGImage, RNSVGLine, RNSVGLinearGradient, RNSVGMarker, RNSVGMask, RNSVGPath, RNSVGPattern, RNSVGRadialGradient, RNSVGRect, RNSVGSvgAndroid, RNSVGSvgIOS, RNSVGSymbol, RNSVGText, RNSVGTextPath, RNSVGTSpan, RNSVGUse, Shape, SvgAst, SvgFromUri, SvgFromXml, SvgUri, SvgXml };
export * from './elements';
export { default } from './elements';
//# sourceMappingURL=ReactNativeSVG.js.map