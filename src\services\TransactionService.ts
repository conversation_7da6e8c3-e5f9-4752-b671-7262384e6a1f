// Transaction Service for Database Operations

import uuid from 'react-native-uuid';
import databaseService from '../database';
import { Transaction } from '../types';

export class TransactionService {
  async getAllTransactions(): Promise<Transaction[]> {
    const query = `
      SELECT t.*, c.name as category_name, a.name as account_name, pm.name as payment_method_name
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      LEFT JOIN accounts a ON t.account_id = a.id
      LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
      ORDER BY t.date DESC
    `;
    
    const rows = await databaseService.executeQuery(query);
    return rows.map(this.mapRowToTransaction);
  }

  async getTransactionById(id: string): Promise<Transaction | null> {
    const query = `
      SELECT t.*, c.name as category_name, a.name as account_name, pm.name as payment_method_name
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      LEFT JOIN accounts a ON t.account_id = a.id
      LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
      WHERE t.id = ?
    `;
    
    const rows = await databaseService.executeQuery(query, [id]);
    return rows.length > 0 ? this.mapRowToTransaction(rows[0]) : null;
  }

  async getTransactionsByDateRange(startDate: Date, endDate: Date): Promise<Transaction[]> {
    const query = `
      SELECT t.*, c.name as category_name, a.name as account_name, pm.name as payment_method_name
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      LEFT JOIN accounts a ON t.account_id = a.id
      LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
      WHERE t.date BETWEEN ? AND ?
      ORDER BY t.date DESC
    `;
    
    const rows = await databaseService.executeQuery(query, [
      startDate.toISOString(),
      endDate.toISOString(),
    ]);
    return rows.map(this.mapRowToTransaction);
  }

  async getTransactionsByCategory(categoryId: string): Promise<Transaction[]> {
    const query = `
      SELECT t.*, c.name as category_name, a.name as account_name, pm.name as payment_method_name
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      LEFT JOIN accounts a ON t.account_id = a.id
      LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
      WHERE t.category_id = ?
      ORDER BY t.date DESC
    `;
    
    const rows = await databaseService.executeQuery(query, [categoryId]);
    return rows.map(this.mapRowToTransaction);
  }

  async getTransactionsByAccount(accountId: string): Promise<Transaction[]> {
    const query = `
      SELECT t.*, c.name as category_name, a.name as account_name, pm.name as payment_method_name
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      LEFT JOIN accounts a ON t.account_id = a.id
      LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
      WHERE t.account_id = ?
      ORDER BY t.date DESC
    `;
    
    const rows = await databaseService.executeQuery(query, [accountId]);
    return rows.map(this.mapRowToTransaction);
  }

  async createTransaction(transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<Transaction> {
    const id = uuid.v4();
    const now = new Date().toISOString();
    
    const query = `
      INSERT INTO transactions (
        id, amount, type, category_id, account_id, payment_method_id,
        description, date, is_recurring, recurring_id, sms_id, is_confirmed,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await databaseService.executeQuery(query, [
      id,
      transaction.amount,
      transaction.type,
      transaction.categoryId,
      transaction.accountId,
      transaction.paymentMethodId,
      transaction.description,
      transaction.date.toISOString(),
      transaction.isRecurring ? 1 : 0,
      transaction.recurringId || null,
      transaction.smsId || null,
      transaction.isConfirmed ? 1 : 0,
      now,
      now,
    ]);

    // Update account balance
    await this.updateAccountBalance(transaction.accountId, transaction.amount, transaction.type);

    const createdTransaction = await this.getTransactionById(id);
    if (!createdTransaction) {
      throw new Error('Failed to create transaction');
    }

    return createdTransaction;
  }

  async updateTransaction(id: string, updates: Partial<Transaction>): Promise<Transaction> {
    const existingTransaction = await this.getTransactionById(id);
    if (!existingTransaction) {
      throw new Error('Transaction not found');
    }

    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (updates.amount !== undefined) {
      updateFields.push('amount = ?');
      updateValues.push(updates.amount);
    }
    if (updates.type !== undefined) {
      updateFields.push('type = ?');
      updateValues.push(updates.type);
    }
    if (updates.categoryId !== undefined) {
      updateFields.push('category_id = ?');
      updateValues.push(updates.categoryId);
    }
    if (updates.accountId !== undefined) {
      updateFields.push('account_id = ?');
      updateValues.push(updates.accountId);
    }
    if (updates.paymentMethodId !== undefined) {
      updateFields.push('payment_method_id = ?');
      updateValues.push(updates.paymentMethodId);
    }
    if (updates.description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(updates.description);
    }
    if (updates.date !== undefined) {
      updateFields.push('date = ?');
      updateValues.push(updates.date.toISOString());
    }
    if (updates.isConfirmed !== undefined) {
      updateFields.push('is_confirmed = ?');
      updateValues.push(updates.isConfirmed ? 1 : 0);
    }

    updateFields.push('updated_at = ?');
    updateValues.push(new Date().toISOString());
    updateValues.push(id);

    const query = `UPDATE transactions SET ${updateFields.join(', ')} WHERE id = ?`;
    await databaseService.executeQuery(query, updateValues);

    // Update account balance if amount or type changed
    if (updates.amount !== undefined || updates.type !== undefined) {
      // Reverse the old transaction
      await this.updateAccountBalance(
        existingTransaction.accountId,
        -existingTransaction.amount,
        existingTransaction.type
      );
      // Apply the new transaction
      await this.updateAccountBalance(
        updates.accountId || existingTransaction.accountId,
        updates.amount || existingTransaction.amount,
        updates.type || existingTransaction.type
      );
    }

    const updatedTransaction = await this.getTransactionById(id);
    if (!updatedTransaction) {
      throw new Error('Failed to update transaction');
    }

    return updatedTransaction;
  }

  async deleteTransaction(id: string): Promise<void> {
    const transaction = await this.getTransactionById(id);
    if (!transaction) {
      throw new Error('Transaction not found');
    }

    // Reverse the transaction from account balance
    await this.updateAccountBalance(transaction.accountId, -transaction.amount, transaction.type);

    const query = 'DELETE FROM transactions WHERE id = ?';
    await databaseService.executeQuery(query, [id]);
  }

  async clearAllTransactions(): Promise<void> {
    const query = 'DELETE FROM transactions';
    await databaseService.executeQuery(query);
  }

  async getMonthlyExpensesByCategory(year: number, month: number): Promise<{ categoryId: string; categoryName: string; amount: number }[]> {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);

    const query = `
      SELECT 
        t.category_id as categoryId,
        c.name as categoryName,
        SUM(t.amount) as amount
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      WHERE t.type = 'expense' 
        AND t.date BETWEEN ? AND ?
      GROUP BY t.category_id, c.name
      ORDER BY amount DESC
    `;

    return await databaseService.executeQuery(query, [
      startDate.toISOString(),
      endDate.toISOString(),
    ]);
  }

  async getMonthlyIncomeVsExpense(year: number, month: number): Promise<{ income: number; expense: number }> {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);

    const query = `
      SELECT 
        type,
        SUM(amount) as total
      FROM transactions
      WHERE date BETWEEN ? AND ?
        AND type IN ('income', 'expense')
      GROUP BY type
    `;

    const rows = await databaseService.executeQuery(query, [
      startDate.toISOString(),
      endDate.toISOString(),
    ]);

    const result = { income: 0, expense: 0 };
    rows.forEach(row => {
      if (row.type === 'income') result.income = row.total;
      if (row.type === 'expense') result.expense = row.total;
    });

    return result;
  }

  private async updateAccountBalance(accountId: string, amount: number, type: string): Promise<void> {
    const balanceChange = type === 'income' ? amount : -amount;
    
    const query = `
      UPDATE accounts 
      SET balance = balance + ?, updated_at = ?
      WHERE id = ?
    `;
    
    await databaseService.executeQuery(query, [
      balanceChange,
      new Date().toISOString(),
      accountId,
    ]);
  }

  private mapRowToTransaction(row: any): Transaction {
    return {
      id: row.id,
      amount: row.amount,
      type: row.type,
      categoryId: row.category_id,
      accountId: row.account_id,
      paymentMethodId: row.payment_method_id,
      description: row.description,
      date: new Date(row.date),
      isRecurring: Boolean(row.is_recurring),
      recurringId: row.recurring_id,
      smsId: row.sms_id,
      isConfirmed: Boolean(row.is_confirmed),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}

export const transactionService = new TransactionService();
