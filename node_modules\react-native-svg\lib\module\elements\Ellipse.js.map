{"version": 3, "names": ["React", "extract", "<PERSON><PERSON><PERSON>", "RNSVGEllipse", "Ellipse", "displayName", "defaultProps", "cx", "cy", "rx", "ry", "render", "props", "ellipseProps", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/Ellipse.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,6BAA6B;AAErD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,YAAY,MAAM,kCAAkC;AAW3D,eAAe,MAAMC,OAAO,SAASF,KAAK,CAAe;EACvD,OAAOG,WAAW,GAAG,SAAS;EAE9B,OAAOC,YAAY,GAAG;IACpBC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE;EACN,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MAAEL,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC;IAAG,CAAC,GAAGE,KAAK;IAChC,MAAMC,YAAY,GAAG;MACnB,GAAGZ,OAAO,CAAC,IAAI,EAAEW,KAAK,CAAC;MACvBL,EAAE;MACFC,EAAE;MACFC,EAAE;MACFC;IACF,CAAC;IACD,oBACEV,KAAA,CAAAc,aAAA,CAACX,YAAY,EAAAY,QAAA;MACXC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAuC;IAAE,GAClEH,YAAY,CACjB,CAAC;EAEN;AACF", "ignoreList": []}