{"version": 3, "names": ["React", "RNSVGFeMerge", "extractFeMerge", "extractFilter", "FilterPrimitive", "FeMerge", "displayName", "defaultProps", "defaultPrimitiveProps", "render", "createElement", "_extends", "ref", "refMethod", "props"], "sourceRoot": "../../../../src", "sources": ["elements/filters/FeMerge.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,SAASC,cAAc,EAAEC,aAAa,QAAQ,iCAAiC;AAC/E,OAAOC,eAAe,MAAM,mBAAmB;AAM/C,eAAe,MAAMC,OAAO,SAASD,eAAe,CAAe;EACjE,OAAOE,WAAW,GAAG,SAAS;EAE9B,OAAOC,YAAY,GAAG;IACpB,GAAG,IAAI,CAACC;EACV,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,oBACET,KAAA,CAAAU,aAAA,CAACT,YAAY,EAAAU,QAAA;MACXC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAuC;IAAE,GAClET,aAAa,CAAC,IAAI,CAACW,KAAK,CAAC,EACzBZ,cAAc,CAAC,IAAI,CAACY,KAAK,EAAE,IAAI,CAAC,CACrC,CAAC;EAEN;AACF", "ignoreList": []}