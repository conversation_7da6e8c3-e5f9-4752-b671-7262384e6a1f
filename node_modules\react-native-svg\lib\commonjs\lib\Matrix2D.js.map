{"version": 3, "names": ["DEG_TO_RAD", "Math", "PI", "identity", "exports", "a", "b", "c", "d", "tx", "ty", "hasInitialState", "reset", "toArray", "append", "a2", "b2", "c2", "d2", "tx2", "ty2", "change", "translate", "a1", "b1", "c1", "d1", "appendTransform", "x", "y", "scaleX", "scaleY", "rotation", "skewX", "skewY", "regX", "regY", "cos", "sin", "r", "tan"], "sourceRoot": "../../../src", "sources": ["lib/Matrix2D.ts"], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA,MAAMA,UAAU,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;AAEzB,MAAMC,QAA0D,GAAAC,OAAA,CAAAD,QAAA,GAAG,CACxE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CACjB;AAED,IAAIE,CAAC,GAAG,CAAC;AACT,IAAIC,CAAC,GAAG,CAAC;AACT,IAAIC,CAAC,GAAG,CAAC;AACT,IAAIC,CAAC,GAAG,CAAC;AACT,IAAIC,EAAE,GAAG,CAAC;AACV,IAAIC,EAAE,GAAG,CAAC;AACV,IAAIC,eAAe,GAAG,IAAI;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACO,SAASC,KAAKA,CAAA,EAAG;EACtB,IAAID,eAAe,EAAE;IACnB;EACF;EACAN,CAAC,GAAGG,CAAC,GAAG,CAAC;EACTF,CAAC,GAAGC,CAAC,GAAGE,EAAE,GAAGC,EAAE,GAAG,CAAC;EACnBC,eAAe,GAAG,IAAI;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASE,OAAOA,CAAA,EAAqD;EAC1E,IAAIF,eAAe,EAAE;IACnB,OAAOR,QAAQ;EACjB;EACA,OAAO,CAACE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,CAAC;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,MAAMA,CACpBC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,GAAW,EACXC,GAAW,EACX;EACA,MAAMC,MAAM,GAAGN,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC;EAC3D,MAAMI,SAAS,GAAGH,GAAG,KAAK,CAAC,IAAIC,GAAG,KAAK,CAAC;EACxC,IAAI,CAACC,MAAM,IAAI,CAACC,SAAS,EAAE;IACzB;EACF;EACA,IAAIX,eAAe,EAAE;IACnBA,eAAe,GAAG,KAAK;IACvBN,CAAC,GAAGU,EAAE;IACNT,CAAC,GAAGU,EAAE;IACNT,CAAC,GAAGU,EAAE;IACNT,CAAC,GAAGU,EAAE;IACNT,EAAE,GAAGU,GAAG;IACRT,EAAE,GAAGU,GAAG;IACR;EACF;EACA,MAAMG,EAAE,GAAGlB,CAAC;EACZ,MAAMmB,EAAE,GAAGlB,CAAC;EACZ,MAAMmB,EAAE,GAAGlB,CAAC;EACZ,MAAMmB,EAAE,GAAGlB,CAAC;EACZ,IAAIa,MAAM,EAAE;IACVhB,CAAC,GAAGkB,EAAE,GAAGR,EAAE,GAAGU,EAAE,GAAGT,EAAE;IACrBV,CAAC,GAAGkB,EAAE,GAAGT,EAAE,GAAGW,EAAE,GAAGV,EAAE;IACrBT,CAAC,GAAGgB,EAAE,GAAGN,EAAE,GAAGQ,EAAE,GAAGP,EAAE;IACrBV,CAAC,GAAGgB,EAAE,GAAGP,EAAE,GAAGS,EAAE,GAAGR,EAAE;EACvB;EACA,IAAII,SAAS,EAAE;IACbb,EAAE,GAAGc,EAAE,GAAGJ,GAAG,GAAGM,EAAE,GAAGL,GAAG,GAAGX,EAAE;IAC7BC,EAAE,GAAGc,EAAE,GAAGL,GAAG,GAAGO,EAAE,GAAGN,GAAG,GAAGV,EAAE;EAC/B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASiB,eAAeA,CAC7BC,CAAS,EACTC,CAAS,EACTC,MAAc,EACdC,MAAc,EACdC,QAAgB,EAChBC,KAAa,EACbC,KAAa,EACbC,IAAY,EACZC,IAAY,EACZ;EACA,IACER,CAAC,KAAK,CAAC,IACPC,CAAC,KAAK,CAAC,IACPC,MAAM,KAAK,CAAC,IACZC,MAAM,KAAK,CAAC,IACZC,QAAQ,KAAK,CAAC,IACdC,KAAK,KAAK,CAAC,IACXC,KAAK,KAAK,CAAC,IACXC,IAAI,KAAK,CAAC,IACVC,IAAI,KAAK,CAAC,EACV;IACA;EACF;EACA,IAAIC,GAAG,EAAEC,GAAG;EACZ,IAAIN,QAAQ,GAAG,GAAG,EAAE;IAClB,MAAMO,CAAC,GAAGP,QAAQ,GAAGhC,UAAU;IAC/BqC,GAAG,GAAGpC,IAAI,CAACoC,GAAG,CAACE,CAAC,CAAC;IACjBD,GAAG,GAAGrC,IAAI,CAACqC,GAAG,CAACC,CAAC,CAAC;EACnB,CAAC,MAAM;IACLF,GAAG,GAAG,CAAC;IACPC,GAAG,GAAG,CAAC;EACT;EAEA,MAAMvB,EAAE,GAAGsB,GAAG,GAAGP,MAAM;EACvB,MAAMd,EAAE,GAAGsB,GAAG,GAAGR,MAAM;EACvB,MAAMb,EAAE,GAAG,CAACqB,GAAG,GAAGP,MAAM;EACxB,MAAMb,EAAE,GAAGmB,GAAG,GAAGN,MAAM;EAEvB,IAAIE,KAAK,IAAIC,KAAK,EAAE;IAClB,MAAMV,EAAE,GAAGvB,IAAI,CAACuC,GAAG,CAACN,KAAK,GAAGlC,UAAU,CAAC;IACvC,MAAMyB,EAAE,GAAGxB,IAAI,CAACuC,GAAG,CAACP,KAAK,GAAGjC,UAAU,CAAC;IACvCc,MAAM,CAACC,EAAE,GAAGU,EAAE,GAAGT,EAAE,EAAEQ,EAAE,GAAGT,EAAE,GAAGC,EAAE,EAAEC,EAAE,GAAGQ,EAAE,GAAGP,EAAE,EAAEM,EAAE,GAAGP,EAAE,GAAGC,EAAE,EAAEU,CAAC,EAAEC,CAAC,CAAC;EACtE,CAAC,MAAM;IACLf,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEU,CAAC,EAAEC,CAAC,CAAC;EAC9B;EAEA,IAAIM,IAAI,IAAIC,IAAI,EAAE;IAChB;IACA3B,EAAE,IAAI0B,IAAI,GAAG9B,CAAC,GAAG+B,IAAI,GAAG7B,CAAC;IACzBG,EAAE,IAAIyB,IAAI,GAAG7B,CAAC,GAAG8B,IAAI,GAAG5B,CAAC;IACzBG,eAAe,GAAG,KAAK;EACzB;AACF", "ignoreList": []}