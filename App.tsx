import 'react-native-gesture-handler';
import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import AppNavigator from './src/navigation/AppNavigator';
import databaseService from './src/database';

export default function App() {
  useEffect(() => {
    // Initialize database on app start
    const initializeApp = async () => {
      try {
        console.log('Starting database initialization...');

        // Add timeout to database initialization
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Database initialization timeout')), 10000)
        );

        await Promise.race([
          databaseService.initializeDatabase(),
          timeoutPromise
        ]);

        console.log('App initialized successfully');
      } catch (error) {
        console.error('Failed to initialize app:', error);
        // Continue anyway - the app should still work with some limitations
      }
    };

    initializeApp();
  }, []);

  return (
    <>
      <StatusBar style="light" />
      <AppNavigator />
    </>
  );
}
