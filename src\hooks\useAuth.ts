// Authentication Hook

import { useState, useEffect, useCallback, useReducer } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { authService, AuthResult } from '../services/AuthService';
import { useAppStore } from '../store';

export interface UseAuthReturn {
  isAuthenticated: boolean;
  isLoading: boolean;
  hasPIN: boolean;
  isBiometricAvailable: boolean;
  isBiometricEnabled: boolean;
  isLocked: boolean;
  authenticate: (pin?: string) => Promise<AuthResult>;
  authenticateWithBiometric: () => Promise<AuthResult>;
  setPIN: (pin: string) => Promise<AuthResult>;
  changePIN: (oldPin: string, newPin: string) => Promise<AuthResult>;
  removePIN: () => Promise<AuthResult>;
  setBiometricEnabled: (enabled: boolean) => Promise<void>;
  setSecurityQuestion: (question: string, answer: string) => Promise<AuthResult>;
  verifySecurityAnswer: (answer: string) => Promise<AuthResult>;
  getSecurityQuestion: () => Promise<string | null>;
  lockApp: () => void;
  unlockApp: () => void;
  logout: () => Promise<void>;
  clearAllAuthData: () => Promise<void>;
}

interface AuthState {
  hasPIN: boolean;
  isAuthenticated: boolean;
  isBiometricAvailable: boolean;
  isBiometricEnabled: boolean;
  isLoading: boolean;
  isLocked: boolean;
}

type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_HAS_PIN'; payload: boolean }
  | { type: 'SET_AUTHENTICATED'; payload: boolean }
  | { type: 'SET_BIOMETRIC_AVAILABLE'; payload: boolean }
  | { type: 'SET_BIOMETRIC_ENABLED'; payload: boolean }
  | { type: 'SET_LOCKED'; payload: boolean }
  | { type: 'SET_AUTH_COMPLETE'; payload: { hasPIN: boolean; isAuthenticated: boolean; isLocked: boolean } }
  | { type: 'RESET_AUTH' };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_HAS_PIN':
      return { ...state, hasPIN: action.payload };
    case 'SET_AUTHENTICATED':
      return { ...state, isAuthenticated: action.payload };
    case 'SET_BIOMETRIC_AVAILABLE':
      return { ...state, isBiometricAvailable: action.payload };
    case 'SET_BIOMETRIC_ENABLED':
      return { ...state, isBiometricEnabled: action.payload };
    case 'SET_LOCKED':
      return { ...state, isLocked: action.payload };
    case 'SET_AUTH_COMPLETE':
      return {
        ...state,
        hasPIN: action.payload.hasPIN,
        isAuthenticated: action.payload.isAuthenticated,
        isLocked: action.payload.isLocked
      };
    case 'RESET_AUTH':
      return {
        hasPIN: false,
        isAuthenticated: false,
        isBiometricAvailable: false,
        isBiometricEnabled: false,
        isLoading: true,
        isLocked: false,
      };
    default:
      return state;
  }
};

export const useAuth = (): UseAuthReturn => {
  const { isAuthenticated, setAuthenticated, updateSettings } = useAppStore();

  const [authState, dispatch] = useReducer(authReducer, {
    hasPIN: false,
    isAuthenticated: false,
    isBiometricAvailable: false,
    isBiometricEnabled: false,
    isLoading: true,
    isLocked: false,
  });

  // Extract values from authState for easier access
  const { hasPIN, isBiometricAvailable, isBiometricEnabled, isLoading, isLocked } = authState;

  // Check authentication status on mount
  useEffect(() => {
    let isMounted = true;

    const initAuth = async () => {
      try {
        console.log('Starting auth initialization...');

        // Simple timeout mechanism
        const timeoutId = setTimeout(() => {
          if (isMounted) {
            console.warn('Auth initialization timed out, using defaults');
            dispatch({ type: 'SET_LOADING', payload: false });
            dispatch({ type: 'SET_HAS_PIN', payload: false });
            setAuthenticated(false);
            dispatch({ type: 'SET_LOCKED', payload: false });
          }
        }, 3000); // 3 second timeout

        await checkAuthStatus();

        clearTimeout(timeoutId);
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (isMounted) {
          dispatch({ type: 'SET_LOADING', payload: false });
          dispatch({ type: 'SET_HAS_PIN', payload: false });
          setAuthenticated(false);
          dispatch({ type: 'SET_LOCKED', payload: false });
        }
      }
    };

    initAuth();

    return () => {
      isMounted = false;
    };
  }, []);

  // Handle app state changes for auto-lock
  useEffect(() => {
    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // App came to foreground
        await authService.setLastActiveTime();
        
        if (isAuthenticated) {
          const shouldLock = await authService.shouldLockApp();
          if (shouldLock) {
            dispatch({ type: 'SET_LOCKED', payload: true });
            setAuthenticated(false);
          }
        }
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        // App went to background
        await authService.setLastActiveTime();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [isAuthenticated, setAuthenticated]);

  const checkAuthStatus = useCallback(async () => {
    try {
      console.log('Starting auth status check...');

      // Check if PIN is set (most critical check)
      console.log('Checking if PIN exists...');
      let pinExists = false;
      try {
        pinExists = await authService.hasPIN();
        console.log('PIN exists:', pinExists);
      } catch (error) {
        console.warn('Failed to check PIN status, assuming false:', error);
        pinExists = false;
      }
      dispatch({ type: 'SET_HAS_PIN', payload: pinExists });

      // Check biometric availability (non-critical)
      console.log('Checking biometric availability...');
      let biometricAvailable = false;
      try {
        const biometricCheck = await authService.isBiometricAvailable();
        biometricAvailable = biometricCheck.available;
        console.log('Biometric available:', biometricAvailable);
      } catch (error) {
        console.warn('Failed to check biometric availability:', error);
      }
      dispatch({ type: 'SET_BIOMETRIC_AVAILABLE', payload: biometricAvailable });

      // Check if biometric is enabled (non-critical)
      console.log('Checking if biometric is enabled...');
      let biometricEnabled = false;
      try {
        biometricEnabled = await authService.isBiometricEnabled();
        console.log('Biometric enabled:', biometricEnabled);
      } catch (error) {
        console.warn('Failed to check biometric enabled status:', error);
      }
      dispatch({ type: 'SET_BIOMETRIC_ENABLED', payload: biometricEnabled });

      // Set authentication state based on PIN existence
      if (!pinExists) {
        console.log('No PIN set, user needs to set up authentication');
        setAuthenticated(false);
        dispatch({ type: 'SET_LOCKED', payload: false });
      } else {
        console.log('PIN exists, checking if app should be locked...');
        // Only check lock status if PIN exists and user was previously authenticated
        if (isAuthenticated) {
          try {
            const shouldLock = await authService.shouldLockApp();
            console.log('Should lock:', shouldLock);
            if (shouldLock) {
              dispatch({ type: 'SET_LOCKED', payload: true });
              setAuthenticated(false);
            }
          } catch (lockError) {
            console.warn('Lock check failed, assuming not locked:', lockError);
          }
        }
      }

      console.log('Auth status check completed successfully');
    } catch (error) {
      console.error('Critical error in auth status check:', error);
      // Set safe defaults on error
      dispatch({ type: 'SET_HAS_PIN', payload: false });
      setAuthenticated(false);
      dispatch({ type: 'SET_LOCKED', payload: false });
      dispatch({ type: 'SET_BIOMETRIC_AVAILABLE', payload: false });
      dispatch({ type: 'SET_BIOMETRIC_ENABLED', payload: false });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
      console.log('Auth loading set to false');
    }
  }, [isAuthenticated, setAuthenticated]);

  const authenticate = useCallback(async (pin?: string): Promise<AuthResult> => {
    try {
      if (pin) {
        // Authenticate with PIN
        const result = await authService.verifyPIN(pin);
        if (result.success) {
          setAuthenticated(true);
          dispatch({ type: 'SET_LOCKED', payload: false });
          await authService.setLastActiveTime();
        }
        return result;
      } else {
        // Try biometric first if available and enabled
        if (isBiometricAvailable && isBiometricEnabled) {
          const biometricResult = await authService.authenticateWithBiometric();
          if (biometricResult.success) {
            setAuthenticated(true);
            dispatch({ type: 'SET_LOCKED', payload: false });
            await authService.setLastActiveTime();
            return biometricResult;
          }
        }
        
        // Return indication that PIN is required
        return { success: false, error: 'PIN_REQUIRED' };
      }
    } catch (error) {
      console.error('Authentication error:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }, [isBiometricAvailable, isBiometricEnabled, setAuthenticated]);

  const authenticateWithBiometric = useCallback(async (): Promise<AuthResult> => {
    try {
      const result = await authService.authenticateWithBiometric();
      if (result.success) {
        setAuthenticated(true);
        dispatch({ type: 'SET_LOCKED', payload: false });
        await authService.setLastActiveTime();
      }
      return result;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return { success: false, error: 'Biometric authentication failed' };
    }
  }, [setAuthenticated]);

  const setPIN = useCallback(async (pin: string): Promise<AuthResult> => {
    try {
      console.log('useAuth setPIN called');
      const result = await authService.setPIN(pin);
      console.log('authService.setPIN result:', result);
      if (result.success) {
        console.log('Setting auth state: hasPIN=true, authenticated=true, isLocked=false');

        // Update states atomically to prevent race conditions
        await authService.setLastActiveTime();

        // Use atomic dispatch to update all auth state at once
        dispatch({
          type: 'SET_AUTH_COMPLETE',
          payload: { hasPIN: true, isAuthenticated: true, isLocked: false }
        });
        setAuthenticated(true);

        console.log('Auth state updated atomically');

        console.log('Post-setPIN state check:', {
          hasPIN: true,
          isAuthenticated: true,
          isLocked: false
        });
      }
      return result;
    } catch (error) {
      console.error('Set PIN error:', error);
      return { success: false, error: 'Failed to set PIN' };
    }
  }, [setAuthenticated]);

  const changePIN = useCallback(async (oldPin: string, newPin: string): Promise<AuthResult> => {
    try {
      return await authService.changePIN(oldPin, newPin);
    } catch (error) {
      console.error('Change PIN error:', error);
      return { success: false, error: 'Failed to change PIN' };
    }
  }, []);

  const removePIN = useCallback(async (): Promise<AuthResult> => {
    try {
      const result = await authService.removePIN();
      if (result.success) {
        dispatch({ type: 'SET_HAS_PIN', payload: false });
        setAuthenticated(false);
        dispatch({ type: 'SET_LOCKED', payload: false });
      }
      return result;
    } catch (error) {
      console.error('Remove PIN error:', error);
      return { success: false, error: 'Failed to remove PIN' };
    }
  }, [setAuthenticated]);

  const setBiometricEnabled = useCallback(async (enabled: boolean): Promise<void> => {
    try {
      await authService.setBiometricEnabled(enabled);
      dispatch({ type: 'SET_BIOMETRIC_ENABLED', payload: enabled });
      updateSettings({ biometricAuth: enabled });
    } catch (error) {
      console.error('Set biometric enabled error:', error);
    }
  }, [updateSettings]);

  const setSecurityQuestion = useCallback(async (question: string, answer: string): Promise<AuthResult> => {
    try {
      return await authService.setSecurityQuestion(question, answer);
    } catch (error) {
      console.error('Set security question error:', error);
      return { success: false, error: 'Failed to set security question' };
    }
  }, []);

  const verifySecurityAnswer = useCallback(async (answer: string): Promise<AuthResult> => {
    try {
      return await authService.verifySecurityAnswer(answer);
    } catch (error) {
      console.error('Verify security answer error:', error);
      return { success: false, error: 'Failed to verify answer' };
    }
  }, []);

  const getSecurityQuestion = useCallback(async (): Promise<string | null> => {
    try {
      return await authService.getSecurityQuestion();
    } catch (error) {
      console.error('Get security question error:', error);
      return null;
    }
  }, []);

  const lockApp = useCallback(() => {
    dispatch({ type: 'SET_LOCKED', payload: true });
    setAuthenticated(false);
  }, [setAuthenticated]);

  const unlockApp = useCallback(() => {
    dispatch({ type: 'SET_LOCKED', payload: false });
    setAuthenticated(true);
  }, [setAuthenticated]);

  const logout = useCallback(async (): Promise<void> => {
    try {
      setAuthenticated(false);
      dispatch({ type: 'SET_LOCKED', payload: false });
      // Note: We don't clear auth data on logout, only on explicit removal
    } catch (error) {
      console.error('Logout error:', error);
    }
  }, [setAuthenticated]);

  const clearAllAuthData = useCallback(async (): Promise<void> => {
    try {
      await authService.clearAllAuthData();
      dispatch({ type: 'RESET_AUTH' });
      setAuthenticated(false);
      console.log('All auth data cleared');
    } catch (error) {
      console.error('Clear auth data error:', error);
    }
  }, [setAuthenticated]);

  // Debug logging for return values
  console.log('useAuth returning:', {
    isAuthenticated,
    isLoading,
    hasPIN,
    isBiometricAvailable,
    isBiometricEnabled,
    isLocked,
  });

  return {
    isAuthenticated,
    isLoading,
    hasPIN,
    isBiometricAvailable,
    isBiometricEnabled,
    isLocked,
    authenticate,
    authenticateWithBiometric,
    setPIN,
    changePIN,
    removePIN,
    setBiometricEnabled,
    setSecurityQuestion,
    verifySecurityAnswer,
    getSecurityQuestion,
    lockApp,
    unlockApp,
    logout,
    clearAllAuthData,
  };
};
