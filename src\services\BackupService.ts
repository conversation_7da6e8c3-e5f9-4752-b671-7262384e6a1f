// Backup Service - Handle data backup, export, and restore functionality

import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as DocumentPicker from 'expo-document-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import { transactionService } from './TransactionService';
import { budgetService } from './BudgetService';
import { investmentService } from './InvestmentService';
import { accountService } from './AccountService';
import { Transaction, Budget, Investment, Account } from '../types';

export interface BackupData {
  version: string;
  timestamp: string;
  data: {
    transactions: Transaction[];
    budgets: Budget[];
    investments: Investment[];
    accounts: Account[];
    settings: any;
  };
}

class BackupService {
  private readonly BACKUP_VERSION = '1.0.0';

  async createBackup(): Promise<BackupData> {
    try {
      // Fetch all data from services
      const [transactions, budgets, investments, accounts] = await Promise.all([
        transactionService.getAllTransactions(),
        budgetService.getAllBudgets(),
        investmentService.getAllInvestments(),
        accountService.getAllAccounts(),
      ]);

      // Get app settings
      const settings = await this.getAppSettings();

      const backupData: BackupData = {
        version: this.BACKUP_VERSION,
        timestamp: new Date().toISOString(),
        data: {
          transactions,
          budgets,
          investments,
          accounts,
          settings,
        },
      };

      return backupData;
    } catch (error) {
      console.error('Error creating backup:', error);
      throw new Error('Failed to create backup');
    }
  }

  async exportToJSON(): Promise<string> {
    try {
      const backupData = await this.createBackup();
      const jsonString = JSON.stringify(backupData, null, 2);
      
      const fileName = `budget_tracker_backup_${new Date().toISOString().split('T')[0]}.json`;
      const fileUri = FileSystem.documentDirectory + fileName;
      
      await FileSystem.writeAsStringAsync(fileUri, jsonString);
      
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/json',
          dialogTitle: 'Export Budget Tracker Data',
        });
      }
      
      return fileUri;
    } catch (error) {
      console.error('Error exporting to JSON:', error);
      throw new Error('Failed to export data');
    }
  }

  async exportToCSV(): Promise<string[]> {
    try {
      const backupData = await this.createBackup();
      const fileUris: string[] = [];

      // Export transactions to CSV
      const transactionsCSV = this.convertTransactionsToCSV(backupData.data.transactions);
      const transactionsFileName = `transactions_${new Date().toISOString().split('T')[0]}.csv`;
      const transactionsFileUri = FileSystem.documentDirectory + transactionsFileName;
      await FileSystem.writeAsStringAsync(transactionsFileUri, transactionsCSV);
      fileUris.push(transactionsFileUri);

      // Export budgets to CSV
      const budgetsCSV = this.convertBudgetsToCSV(backupData.data.budgets);
      const budgetsFileName = `budgets_${new Date().toISOString().split('T')[0]}.csv`;
      const budgetsFileUri = FileSystem.documentDirectory + budgetsFileName;
      await FileSystem.writeAsStringAsync(budgetsFileUri, budgetsCSV);
      fileUris.push(budgetsFileUri);

      // Export investments to CSV
      const investmentsCSV = this.convertInvestmentsToCSV(backupData.data.investments);
      const investmentsFileName = `investments_${new Date().toISOString().split('T')[0]}.csv`;
      const investmentsFileUri = FileSystem.documentDirectory + investmentsFileName;
      await FileSystem.writeAsStringAsync(investmentsFileUri, investmentsCSV);
      fileUris.push(investmentsFileUri);

      // Share all CSV files
      if (await Sharing.isAvailableAsync()) {
        for (const fileUri of fileUris) {
          await Sharing.shareAsync(fileUri, {
            mimeType: 'text/csv',
            dialogTitle: 'Export CSV Data',
          });
        }
      }

      return fileUris;
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      throw new Error('Failed to export CSV data');
    }
  }

  async importFromJSON(): Promise<boolean> {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/json',
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        return false;
      }

      const fileContent = await FileSystem.readAsStringAsync(result.assets[0].uri);
      const backupData: BackupData = JSON.parse(fileContent);

      // Validate backup data
      if (!this.validateBackupData(backupData)) {
        throw new Error('Invalid backup file format');
      }

      // Show confirmation dialog
      return new Promise((resolve) => {
        Alert.alert(
          'Import Data',
          `This will replace all existing data with backup from ${new Date(backupData.timestamp).toLocaleDateString()}. This action cannot be undone.`,
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => resolve(false),
            },
            {
              text: 'Import',
              style: 'destructive',
              onPress: async () => {
                try {
                  await this.restoreFromBackup(backupData);
                  resolve(true);
                } catch (error) {
                  console.error('Error restoring backup:', error);
                  Alert.alert('Error', 'Failed to restore backup');
                  resolve(false);
                }
              },
            },
          ]
        );
      });
    } catch (error) {
      console.error('Error importing JSON:', error);
      throw new Error('Failed to import data');
    }
  }

  private async restoreFromBackup(backupData: BackupData): Promise<void> {
    try {
      // Clear existing data
      await Promise.all([
        transactionService.clearAllTransactions(),
        budgetService.clearAllBudgets(),
        investmentService.clearAllInvestments(),
        accountService.clearAllAccounts(),
      ]);

      // Restore data
      const { transactions, budgets, investments, accounts, settings } = backupData.data;

      // Restore accounts first (as they may be referenced by other entities)
      for (const account of accounts) {
        await accountService.createAccount(account);
      }

      // Restore transactions
      for (const transaction of transactions) {
        await transactionService.createTransaction(transaction);
      }

      // Restore budgets
      for (const budget of budgets) {
        await budgetService.createBudget(budget);
      }

      // Restore investments
      for (const investment of investments) {
        await investmentService.createInvestment(investment);
      }

      // Restore settings
      if (settings) {
        await this.restoreAppSettings(settings);
      }

      Alert.alert('Success', 'Data restored successfully!');
    } catch (error) {
      console.error('Error restoring backup:', error);
      throw error;
    }
  }

  private validateBackupData(data: any): data is BackupData {
    return (
      data &&
      typeof data.version === 'string' &&
      typeof data.timestamp === 'string' &&
      data.data &&
      Array.isArray(data.data.transactions) &&
      Array.isArray(data.data.budgets) &&
      Array.isArray(data.data.investments) &&
      Array.isArray(data.data.accounts)
    );
  }

  private convertTransactionsToCSV(transactions: Transaction[]): string {
    const headers = ['ID', 'Type', 'Amount', 'Description', 'Category', 'Account', 'Date', 'Created At'];
    const rows = transactions.map(t => [
      t.id,
      t.type,
      t.amount.toString(),
      `"${t.description}"`,
      t.categoryId,
      t.accountId,
      new Date(t.date).toLocaleDateString(),
      new Date(t.createdAt).toLocaleDateString(),
    ]);

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  }

  private convertBudgetsToCSV(budgets: Budget[]): string {
    const headers = ['ID', 'Name', 'Amount', 'Spent', 'Category', 'Period', 'Start Date', 'End Date'];
    const rows = budgets.map(b => [
      b.id,
      `"${b.name}"`,
      b.amount.toString(),
      b.spent.toString(),
      b.categoryId,
      b.period,
      new Date(b.startDate).toLocaleDateString(),
      new Date(b.endDate).toLocaleDateString(),
    ]);

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  }

  private convertInvestmentsToCSV(investments: Investment[]): string {
    const headers = ['ID', 'Name', 'Type', 'Symbol', 'Quantity', 'Purchase Price', 'Current Price', 'Purchase Date'];
    const rows = investments.map(i => [
      i.id,
      `"${i.name}"`,
      i.type,
      i.symbol || '',
      (i.quantity || 0).toString(),
      (i.purchasePrice || 0).toString(),
      (i.currentPrice || 0).toString(),
      new Date(i.purchaseDate).toLocaleDateString(),
    ]);

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  }

  private async getAppSettings(): Promise<any> {
    try {
      const settings = await AsyncStorage.getItem('appSettings');
      return settings ? JSON.parse(settings) : {};
    } catch (error) {
      console.error('Error getting app settings:', error);
      return {};
    }
  }

  private async restoreAppSettings(settings: any): Promise<void> {
    try {
      await AsyncStorage.setItem('appSettings', JSON.stringify(settings));
    } catch (error) {
      console.error('Error restoring app settings:', error);
    }
  }

  async getBackupInfo(): Promise<{ size: string; lastBackup: string | null }> {
    try {
      const backupData = await this.createBackup();
      const jsonString = JSON.stringify(backupData);
      const sizeInBytes = new Blob([jsonString]).size;
      const sizeInKB = (sizeInBytes / 1024).toFixed(2);
      
      const lastBackup = await AsyncStorage.getItem('lastBackupDate');
      
      return {
        size: `${sizeInKB} KB`,
        lastBackup: lastBackup ? new Date(lastBackup).toLocaleDateString() : null,
      };
    } catch (error) {
      console.error('Error getting backup info:', error);
      return { size: 'Unknown', lastBackup: null };
    }
  }

  async recordBackup(): Promise<void> {
    try {
      await AsyncStorage.setItem('lastBackupDate', new Date().toISOString());
    } catch (error) {
      console.error('Error recording backup:', error);
    }
  }
}

export const backupService = new BackupService();
