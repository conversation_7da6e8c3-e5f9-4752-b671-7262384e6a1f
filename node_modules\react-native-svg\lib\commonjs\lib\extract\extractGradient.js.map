{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "React", "_reactNative", "_extractOpacity", "_interopRequireDefault", "_extractTransform", "_units", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "percentReg", "percentToFloat", "percent", "__getAnimatedValue", "matched", "match", "console", "warn", "offsetComparator", "object", "other", "extractGradient", "props", "parent", "id", "children", "gradientTransform", "transform", "gradientUnits", "stops", "<PERSON><PERSON><PERSON><PERSON>", "Children", "map", "child", "cloneElement", "l", "length", "style", "offset", "stopColor", "stopOpacity", "offsetNumber", "color", "processColor", "isNaN", "alpha", "Math", "round", "extractOpacity", "push", "sort", "gradient", "k", "j", "s", "name", "units", "extractTransform"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractGradient.ts"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA+B,IAAAC,KAAA,GAAAH,MAAA;AAE/B,IAAAI,YAAA,GAAAF,OAAA;AAEA,IAAAG,eAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,iBAAA,GAAAD,sBAAA,CAAAJ,OAAA;AAEA,IAAAM,MAAA,GAAAF,sBAAA,CAAAJ,OAAA;AAA6B,SAAAI,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAE7B,MAAMW,UAAU,GAAG,6CAA6C;AAEhE,SAASC,cAAcA,CACrBC,OAKK,EACG;EACR,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOA,OAAO;EAChB;EACA,IACE,OAAOA,OAAO,KAAK,QAAQ,IAC3B,OAAOA,OAAO,CAACC,kBAAkB,KAAK,UAAU,EAChD;IACA,OAAOD,OAAO,CAACC,kBAAkB,CAAC,CAAC;EACrC;EACA,MAAMC,OAAO,GAAG,OAAOF,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACG,KAAK,CAACL,UAAU,CAAC;EACxE,IAAI,CAACI,OAAO,EAAE;IACZE,OAAO,CAACC,IAAI,CAAC,IAAIL,OAAO,+CAA+C,CAAC;IACxE,OAAO,CAAC;EACV;EAEA,OAAOE,OAAO,CAAC,CAAC,CAAC,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC;AACrD;AAEA,MAAMI,gBAAgB,GAAGA,CAACC,MAAgB,EAAEC,KAAe,KACzDD,MAAM,CAAC,CAAC,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC;AAEP,SAASC,eAAeA,CACrCC,KAMkB,EAClBC,MAAe,EACf;EACA,MAAM;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,iBAAiB;IAAEC,SAAS;IAAEC;EAAc,CAAC,GAAGN,KAAK;EAC3E,IAAI,CAACE,EAAE,EAAE;IACP,OAAO,IAAI;EACb;EAEA,MAAMK,KAAK,GAAG,EAAE;EAChB,MAAMC,UAAU,GAAGL,QAAQ,GACvBM,eAAQ,CAACC,GAAG,CAACP,QAAQ,EAAGQ,KAAK,iBAC3BjD,KAAK,CAACkD,YAAY,CAACD,KAAK,EAAE;IACxBV;EACF,CAAC,CACH,CAAC,GACD,EAAE;EACN,MAAMY,CAAC,GAAGL,UAAU,CAACM,MAAM;EAC3B,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,CAAC,EAAE3B,CAAC,EAAE,EAAE;IAC1B,MAAM;MACJc,KAAK,EAAE;QACLe,KAAK;QACLC,MAAM,GAAGD,KAAK,IAAIA,KAAK,CAACC,MAAM;QAC9BC,SAAS,GAAIF,KAAK,IAAIA,KAAK,CAACE,SAAS,IAAK,MAAM;QAChDC,WAAW,GAAGH,KAAK,IAAIA,KAAK,CAACG;MAC/B;IACF,CAAC,GAAGV,UAAU,CAACtB,CAAC,CAAC;IACjB,MAAMiC,YAAY,GAAG9B,cAAc,CAAC2B,MAAM,IAAI,CAAC,CAAC;IAChD,MAAMI,KAAK,GAAGH,SAAS,IAAI,IAAAI,yBAAY,EAACJ,SAAS,CAAC;IAClD,IAAI,OAAOG,KAAK,KAAK,QAAQ,IAAIE,KAAK,CAACH,YAAY,CAAC,EAAE;MACpDzB,OAAO,CAACC,IAAI,CACV,IAAIsB,SAAS,8BAA8BD,MAAM,yBACnD,CAAC;MACD;IACF;IACA,MAAMO,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAAC,uBAAc,EAACR,WAAW,CAAC,GAAG,GAAG,CAAC;IAC3DX,KAAK,CAACoB,IAAI,CAAC,CAACR,YAAY,EAAGC,KAAK,GAAG,UAAU,GAAKG,KAAK,IAAI,EAAG,CAAC,CAAC;EAClE;EACAhB,KAAK,CAACqB,IAAI,CAAChC,gBAAgB,CAAC;EAE5B,MAAMiC,QAAQ,GAAG,EAAE;EACnB,MAAMC,CAAC,GAAGvB,KAAK,CAACO,MAAM;EACtB,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;IAC1B,MAAMC,CAAC,GAAGzB,KAAK,CAACwB,CAAC,CAAC;IAClBF,QAAQ,CAACF,IAAI,CAACK,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;EAEA,OAAO;IACLC,IAAI,EAAE/B,EAAE;IACR2B,QAAQ;IACR1B,QAAQ,EAAEK,UAAU;IACpBF,aAAa,EAAGA,aAAa,IAAI4B,cAAK,CAAC5B,aAAa,CAAC,IAAK,CAAC;IAC3DF,iBAAiB,EAAE,IAAA+B,yBAAgB,EACjC/B,iBAAiB,IAAIC,SAAS,IAAIL,KACpC;EACF,CAAC;AACH", "ignoreList": []}