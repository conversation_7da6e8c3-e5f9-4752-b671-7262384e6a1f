// Core Types for Budget Tracker App

export interface User {
  id: string;
  pin?: string;
  biometricEnabled: boolean;
  securityQuestion?: string;
  securityAnswer?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Account {
  id: string;
  name: string;
  type: 'bank' | 'credit_card' | 'cash' | 'savings';
  balance: number;
  currency: string;
  accountNumber?: string;
  bankName?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
  color: string;
  icon: string;
  isDefault: boolean;
  parentId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentMethod {
  id: string;
  name: string;
  type: 'cash' | 'card' | 'upi' | 'net_banking' | 'wallet';
  accountId?: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Transaction {
  id: string;
  amount: number;
  type: 'income' | 'expense' | 'transfer';
  categoryId: string;
  accountId: string;
  paymentMethodId: string;
  description: string;
  date: Date;
  isRecurring: boolean;
  recurringId?: string;
  smsId?: string;
  isConfirmed: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface RecurringTransaction {
  id: string;
  amount: number;
  type: 'income' | 'expense';
  categoryId: string;
  accountId: string;
  paymentMethodId: string;
  description: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  startDate: Date;
  endDate?: Date;
  nextDueDate: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Budget {
  id: string;
  name: string;
  categoryId: string;
  amount: number;
  period: 'weekly' | 'monthly' | 'yearly';
  startDate: Date;
  endDate: Date;
  spent: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Investment {
  id: string;
  type: 'stock' | 'mutual_fund' | 'fd' | 'rd';
  name: string;
  symbol?: string;
  quantity?: number;
  purchasePrice?: number;
  currentPrice?: number;
  purchaseDate: Date;
  maturityDate?: Date;
  interestRate?: number;
  monthlyInstallment?: number;
  totalInvested: number;
  currentValue: number;
  gainLoss: number;
  gainLossPercentage: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SMSTransaction {
  id: string;
  smsBody: string;
  sender: string;
  receivedAt: Date;
  amount?: number;
  transactionType?: 'debit' | 'credit';
  merchant?: string;
  accountNumber?: string;
  isProcessed: boolean;
  transactionId?: string;
  createdAt: Date;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'budget_alert' | 'recurring_reminder' | 'investment_maturity' | 'general';
  isRead: boolean;
  scheduledFor?: Date;
  createdAt: Date;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  PinSetup: undefined;
  PINSetup: undefined;
  PinEntry: undefined;
  PINLogin: undefined;
  PINRecovery: undefined;
  AddTransaction: { type?: 'income' | 'expense' };
  EditTransaction: { transactionId: string };
  TransactionDetails: { transactionId: string };
  AddBudget: undefined;
  EditBudget: { budgetId: string };
  BudgetDetails: { budgetId: string };
  AddInvestment: undefined;
  EditInvestment: { investmentId: string };
  InvestmentDetails: { investmentId: string };
  Accounts: undefined;
  Categories: undefined;
  Security: undefined;
  Backup: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Transactions: undefined;
  Budget: undefined;
  Budgets: undefined;
  Investments: undefined;
  Settings: undefined;
};

export type TransactionStackParamList = {
  TransactionList: undefined;
  AddTransaction: { type?: 'income' | 'expense' };
  EditTransaction: { transactionId: string };
  TransactionDetails: { transactionId: string };
};

export type InvestmentStackParamList = {
  InvestmentDashboard: undefined;
  AddInvestment: { type?: 'stock' | 'mutual_fund' | 'fd' | 'rd' };
  EditInvestment: { investmentId: string };
  InvestmentDetails: { investmentId: string };
};

// API Response Types
export interface StockPrice {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  lastUpdated: Date;
}

export interface MutualFundNAV {
  schemeCode: string;
  schemeName: string;
  nav: number;
  date: Date;
}

// Chart Data Types
export interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    color?: (opacity: number) => string;
    strokeWidth?: number;
  }[];
}

export interface PieChartData {
  name: string;
  population: number;
  color: string;
  legendFontColor: string;
  legendFontSize: number;
}

// Settings Types
export interface AppSettings {
  currency: string;
  dateFormat: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: boolean;
  biometricAuth: boolean;
  autoLock: boolean;
  autoLockTimeout: number;
  smsPermission: boolean;
  backupEnabled: boolean;
  lastBackupDate?: Date;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// State Types
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  accounts: Account[];
  categories: Category[];
  paymentMethods: PaymentMethod[];
  transactions: Transaction[];
  budgets: Budget[];
  investments: Investment[];
  settings: AppSettings;
  isLoading: boolean;
  error: AppError | null;
}
