{"version": 3, "names": ["React", "extractViewBox", "<PERSON><PERSON><PERSON>", "RNSVGSymbol", "Symbol", "displayName", "render", "props", "id", "children", "symbolProps", "name", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/Symbol.tsx"], "mappings": ";AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,WAAW,MAAM,iCAAiC;AAYzD,eAAe,MAAMC,MAAM,SAASF,KAAK,CAAc;EACrD,OAAOG,WAAW,GAAG,QAAQ;EAE7BC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MAAEC,EAAE;MAAEC;IAAS,CAAC,GAAGF,KAAK;IAC9B,MAAMG,WAAW,GAAG;MAAEC,IAAI,EAAEH;IAAG,CAAC;IAChC,oBACER,KAAA,CAAAY,aAAA,CAACT,WAAW,EAAAU,QAAA;MACVC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAyC;IAAE,GACpEJ,WAAW,EACXT,cAAc,CAACM,KAAK,CAAC,GACxBE,QACU,CAAC;EAElB;AACF", "ignoreList": []}