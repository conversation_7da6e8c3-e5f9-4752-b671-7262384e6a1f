// PIN Recovery Screen - Security Question Verification

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../hooks/useAuth';
import { COLORS } from '../../constants';

interface PINRecoveryScreenProps {
  navigation: any;
}

const PINRecoveryScreen: React.FC<PINRecoveryScreenProps> = ({ navigation }) => {
  const [securityQuestion, setSecurityQuestion] = useState<string | null>(null);
  const [answer, setAnswer] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'loading' | 'question' | 'newPin'>('loading');
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  
  const { 
    getSecurityQuestion, 
    verifySecurityAnswer, 
    setPIN,
    removePIN 
  } = useAuth();

  useEffect(() => {
    loadSecurityQuestion();
  }, []);

  const loadSecurityQuestion = async () => {
    try {
      const question = await getSecurityQuestion();
      if (question) {
        setSecurityQuestion(question);
        setStep('question');
      } else {
        Alert.alert(
          'No Security Question',
          'No security question has been set up. You will need to reset the app to create a new PIN.',
          [
            { text: 'Cancel', onPress: () => navigation.goBack() },
            { text: 'Reset App', onPress: handleAppReset, style: 'destructive' }
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load security question');
      navigation.goBack();
    }
  };

  const handleAppReset = async () => {
    Alert.alert(
      'Confirm Reset',
      'This will remove all authentication data and you will need to set up a new PIN. Your transaction data will remain safe.',
      [
        { text: 'Cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: async () => {
            try {
              await removePIN();
              // Navigation will be handled by AppNavigator when PIN is removed
            } catch (error) {
              Alert.alert('Error', 'Failed to reset app');
            }
          }
        }
      ]
    );
  };

  const handleAnswerSubmit = async () => {
    if (!answer.trim()) {
      Alert.alert('Error', 'Please enter your answer');
      return;
    }

    setIsLoading(true);
    try {
      const result = await verifySecurityAnswer(answer);
      if (result.success) {
        setStep('newPin');
      } else {
        Alert.alert('Error', result.error || 'Incorrect answer');
        setAnswer('');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to verify answer');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewPinSubmit = async () => {
    if (newPin.length < 4 || newPin.length > 6) {
      Alert.alert('Error', 'PIN must be 4-6 digits');
      return;
    }

    if (newPin !== confirmPin) {
      Alert.alert('Error', 'PINs do not match');
      return;
    }

    setIsLoading(true);
    try {
      const result = await setPIN(newPin);
      if (result.success) {
        Alert.alert(
          'Success',
          'Your PIN has been reset successfully',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      } else {
        Alert.alert('Error', result.error || 'Failed to set new PIN');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to set new PIN');
    } finally {
      setIsLoading(false);
    }
  };

  const renderQuestionStep = () => (
    <View style={styles.content}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionLabel}>Security Question:</Text>
        <Text style={styles.questionText}>{securityQuestion}</Text>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Your Answer:</Text>
        <TextInput
          style={styles.textInput}
          value={answer}
          onChangeText={setAnswer}
          placeholder="Enter your answer"
          placeholderTextColor={COLORS.TEXT_SECONDARY}
          autoCapitalize="none"
          autoCorrect={false}
          returnKeyType="done"
          onSubmitEditing={handleAnswerSubmit}
        />
      </View>

      <TouchableOpacity
        style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
        onPress={handleAnswerSubmit}
        disabled={isLoading}
      >
        <Text style={styles.submitButtonText}>
          {isLoading ? 'Verifying...' : 'Verify Answer'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.resetButton}
        onPress={handleAppReset}
      >
        <Text style={styles.resetButtonText}>
          Can't remember? Reset app instead
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderNewPinStep = () => (
    <View style={styles.content}>
      <Text style={styles.stepTitle}>Set New PIN</Text>
      <Text style={styles.stepSubtitle}>
        Create a new 4-6 digit PIN to secure your account
      </Text>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>New PIN:</Text>
        <TextInput
          style={styles.textInput}
          value={newPin}
          onChangeText={setNewPin}
          placeholder="Enter new PIN"
          placeholderTextColor={COLORS.TEXT_SECONDARY}
          keyboardType="numeric"
          secureTextEntry
          maxLength={6}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Confirm PIN:</Text>
        <TextInput
          style={styles.textInput}
          value={confirmPin}
          onChangeText={setConfirmPin}
          placeholder="Confirm new PIN"
          placeholderTextColor={COLORS.TEXT_SECONDARY}
          keyboardType="numeric"
          secureTextEntry
          maxLength={6}
          returnKeyType="done"
          onSubmitEditing={handleNewPinSubmit}
        />
      </View>

      <TouchableOpacity
        style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
        onPress={handleNewPinSubmit}
        disabled={isLoading}
      >
        <Text style={styles.submitButtonText}>
          {isLoading ? 'Setting PIN...' : 'Set New PIN'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.PRIMARY} />
          </TouchableOpacity>
          <Text style={styles.title}>Recover PIN</Text>
        </View>

        {step === 'question' && renderQuestionStep()}
        {step === 'newPin' && renderNewPinStep()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  keyboardAvoid: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 40,
  },
  questionContainer: {
    marginBottom: 30,
  },
  questionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 8,
  },
  questionText: {
    fontSize: 18,
    color: COLORS.PRIMARY,
    fontWeight: '500',
    lineHeight: 24,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    backgroundColor: COLORS.WHITE,
  },
  submitButton: {
    backgroundColor: COLORS.PRIMARY,
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonDisabled: {
    backgroundColor: COLORS.DISABLED,
  },
  submitButtonText: {
    color: COLORS.WHITE,
    fontSize: 16,
    fontWeight: '600',
  },
  resetButton: {
    marginTop: 20,
    alignItems: 'center',
  },
  resetButtonText: {
    color: COLORS.ERROR,
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: 8,
  },
  stepSubtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: 30,
  },
});

export default PINRecoveryScreen;
