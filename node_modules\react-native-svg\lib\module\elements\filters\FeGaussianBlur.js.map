{"version": 3, "names": ["React", "RNSVGFeGaussianBlur", "extractFeGaussianBlur", "extractFilter", "extractIn", "FilterPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName", "defaultProps", "defaultPrimitiveProps", "stdDeviation", "edgeMode", "render", "createElement", "_extends", "ref", "refMethod", "props"], "sourceRoot": "../../../../src", "sources": ["elements/filters/FeGaussianBlur.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,SACEC,qBAAqB,EACrBC,aAAa,EACbC,SAAS,QACJ,iCAAiC;AAExC,OAAOC,eAAe,MAAM,mBAAmB;AAW/C,eAAe,MAAMC,cAAc,SAASD,eAAe,CAAsB;EAC/E,OAAOE,WAAW,GAAG,gBAAgB;EAErC,OAAOC,YAAY,GAAgD;IACjE,GAAG,IAAI,CAACC,qBAAqB;IAC7BC,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE;EACZ,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,oBACEZ,KAAA,CAAAa,aAAA,CAACZ,mBAAmB,EAAAa,QAAA;MAClBC,GAAG,EAAGA,GAAG,IACP,IAAI,CAACC,SAAS,CAACD,GAA8C;IAC9D,GACGZ,aAAa,CAAC,IAAI,CAACc,KAAK,CAAC,EACzBb,SAAS,CAAC,IAAI,CAACa,KAAK,CAAC,EACrBf,qBAAqB,CAAC,IAAI,CAACe,KAAK,CAAC,CACtC,CAAC;EAEN;AACF", "ignoreList": []}