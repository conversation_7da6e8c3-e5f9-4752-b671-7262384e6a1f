{"version": 3, "names": ["React", "RNSVGFeOffset", "extractFilter", "extractIn", "FilterPrimitive", "FeOffset", "displayName", "defaultProps", "defaultPrimitiveProps", "dx", "dy", "render", "createElement", "_extends", "ref", "refMethod", "props"], "sourceRoot": "../../../../src", "sources": ["elements/filters/FeOffset.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAOC,aAAa,MAAM,sCAAsC;AAChE,SAASC,aAAa,EAAEC,SAAS,QAAQ,iCAAiC;AAE1E,OAAOC,eAAe,MAAM,mBAAmB;AAQ/C,eAAe,MAAMC,QAAQ,SAASD,eAAe,CAAgB;EACnE,OAAOE,WAAW,GAAG,UAAU;EAE/B,OAAOC,YAAY,GAA0C;IAC3D,GAAG,IAAI,CAACC,qBAAqB;IAC7BC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE;EACN,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,oBACEX,KAAA,CAAAY,aAAA,CAACX,aAAa,EAAAY,QAAA;MACZC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAwC;IAAE,GACnE,IAAI,CAACE,KAAK,EACVd,aAAa,CAAC,IAAI,CAACc,KAAK,CAAC,EACzBb,SAAS,CAAC,IAAI,CAACa,KAAK,CAAC,CAC1B,CAAC;EAEN;AACF", "ignoreList": []}