{"version": 3, "file": "elements.d.ts", "sourceRoot": "", "sources": ["../../src/elements.ts"], "names": [], "mappings": "AAAA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AACvC,OAAO,QAAQ,MAAM,qBAAqB,CAAC;AAC3C,OAAO,IAAI,MAAM,iBAAiB,CAAC;AACnC,OAAO,OAAO,MAAM,oBAAoB,CAAC;AACzC,OAAO,aAAa,MAAM,0BAA0B,CAAC;AACrD,OAAO,CAAC,MAAM,cAAc,CAAC;AAC7B,OAAO,KAAK,MAAM,kBAAkB,CAAC;AACrC,OAAO,IAAI,MAAM,iBAAiB,CAAC;AACnC,OAAO,cAAc,MAAM,2BAA2B,CAAC;AACvD,OAAO,MAAM,MAAM,mBAAmB,CAAC;AACvC,OAAO,IAAI,MAAM,iBAAiB,CAAC;AACnC,OAAO,IAAI,MAAM,iBAAiB,CAAC;AACnC,OAAO,OAAO,MAAM,oBAAoB,CAAC;AACzC,OAAO,OAAO,MAAM,oBAAoB,CAAC;AACzC,OAAO,QAAQ,MAAM,qBAAqB,CAAC;AAC3C,OAAO,cAAc,MAAM,2BAA2B,CAAC;AACvD,OAAO,IAAI,MAAM,iBAAiB,CAAC;AACnC,OAAO,IAAI,MAAM,iBAAiB,CAAC;AACnC,OAAO,GAAG,MAAM,gBAAgB,CAAC;AACjC,OAAO,MAAM,MAAM,mBAAmB,CAAC;AACvC,OAAO,KAAK,MAAM,kBAAkB,CAAC;AACrC,OAAO,IAAI,MAAM,iBAAiB,CAAC;AACnC,OAAO,QAAQ,MAAM,qBAAqB,CAAC;AAC3C,OAAO,GAAG,MAAM,gBAAgB,CAAC;AACjC,OAAO,OAAO,MAAM,4BAA4B,CAAC;AACjD,OAAO,aAAa,MAAM,kCAAkC,CAAC;AAC7D,OAAO,mBAAmB,MAAM,wCAAwC,CAAC;AACzE,OAAO,EACL,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACR,MAAM,gDAAgD,CAAC;AACxD,OAAO,WAAW,MAAM,gCAAgC,CAAC;AACzD,OAAO,gBAAgB,MAAM,qCAAqC,CAAC;AACnE,OAAO,iBAAiB,MAAM,sCAAsC,CAAC;AACrE,OAAO,iBAAiB,MAAM,sCAAsC,CAAC;AACrE,OAAO,cAAc,MAAM,mCAAmC,CAAC;AAC/D,OAAO,YAAY,MAAM,iCAAiC,CAAC;AAC3D,OAAO,OAAO,MAAM,4BAA4B,CAAC;AACjD,OAAO,cAAc,MAAM,mCAAmC,CAAC;AAC/D,OAAO,OAAO,MAAM,4BAA4B,CAAC;AACjD,OAAO,OAAO,MAAM,4BAA4B,CAAC;AACjD,OAAO,WAAW,MAAM,gCAAgC,CAAC;AACzD,OAAO,YAAY,MAAM,iCAAiC,CAAC;AAC3D,OAAO,QAAQ,MAAM,6BAA6B,CAAC;AACnD,OAAO,YAAY,MAAM,iCAAiC,CAAC;AAC3D,OAAO,kBAAkB,MAAM,uCAAuC,CAAC;AACvE,OAAO,WAAW,MAAM,gCAAgC,CAAC;AACzD,OAAO,MAAM,MAAM,2BAA2B,CAAC;AAC/C,OAAO,YAAY,MAAM,iCAAiC,CAAC;AAC3D,OAAO,MAAM,MAAM,2BAA2B,CAAC;AAE/C,OAAO,EACL,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,OAAO,EACP,aAAa,EACb,mBAAmB,EACnB,WAAW,EACX,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,cAAc,EACd,YAAY,EACZ,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,cAAc,EACd,OAAO,EACP,OAAO,EACP,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,kBAAkB,EAClB,WAAW,EACX,MAAM,EACN,YAAY,EACZ,MAAM,EACN,aAAa,EACb,CAAC,EACD,KAAK,EACL,IAAI,EACJ,cAAc,EACd,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACP,QAAQ,EACR,cAAc,EACd,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,GAAG,GACJ,CAAC;AAEF,eAAe,GAAG,CAAC"}