import * as React from 'react';
import extractProps, { propsAndStyles } from '../lib/extract/extractProps';
import extractTransform from '../lib/extract/extractTransform';
import extractText, { setTSpan } from '../lib/extract/extractText';
import { pickNotNil } from '../lib/util';
import Shape from './Shape';
import RNSVGTSpan from '../fabric/TSpanNativeComponent';
export default class TSpan extends Shape {
  static displayName = 'TSpan';
  setNativeProps = props => {
    const matrix = !props.matrix && extractTransform(props);
    if (matrix) {
      props.matrix = matrix;
    }
    const prop = propsAndStyles(props);
    Object.assign(prop, pickNotNil(extractText(prop, false)));
    this.root && this.root.setNativeProps(prop);
  };
  render() {
    const prop = propsAndStyles(this.props);
    const props = extractProps({
      ...prop,
      x: null,
      y: null
    }, this);
    Object.assign(props, extractText(prop, false));
    props.ref = this.refMethod;
    return /*#__PURE__*/React.createElement(RNSVGTSpan, props);
  }
}
setTSpan(TSpan);
//# sourceMappingURL=TSpan.js.map