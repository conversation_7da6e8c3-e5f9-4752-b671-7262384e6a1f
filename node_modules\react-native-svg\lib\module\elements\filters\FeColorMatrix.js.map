{"version": 3, "names": ["React", "RNSVGFeColorMatrix", "extractFeColorMatrix", "extractFilter", "extractIn", "FilterPrimitive", "FeColorMatrix", "displayName", "defaultProps", "defaultPrimitiveProps", "type", "values", "render", "createElement", "_extends", "ref", "refMethod", "props"], "sourceRoot": "../../../../src", "sources": ["elements/filters/FeColorMatrix.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAOC,kBAAkB,MAAM,2CAA2C;AAC1E,SACEC,oBAAoB,EACpBC,aAAa,EACbC,SAAS,QACJ,iCAAiC;AAExC,OAAOC,eAAe,MAAM,mBAAmB;AAQ/C,eAAe,MAAMC,aAAa,SAASD,eAAe,CAAqB;EAC7E,OAAOE,WAAW,GAAG,eAAe;EAEpC,OAAOC,YAAY,GAA+C;IAChE,GAAG,IAAI,CAACC,qBAAqB;IAC7BC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE;EACV,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,oBACEZ,KAAA,CAAAa,aAAA,CAACZ,kBAAkB,EAAAa,QAAA;MACjBC,GAAG,EAAGA,GAAG,IACP,IAAI,CAACC,SAAS,CAACD,GAA6C;IAC7D,GACGZ,aAAa,CAAC,IAAI,CAACc,KAAK,CAAC,EACzBb,SAAS,CAAC,IAAI,CAACa,KAAK,CAAC,EACrBf,oBAAoB,CAAC,IAAI,CAACe,KAAK,CAAC,CACrC,CAAC;EAEN;AACF", "ignoreList": []}