// SMS Service - Handle SMS reading and permission management

import { PermissionsAndroid, Platform } from 'react-native';
import SmsAndroid from 'react-native-get-sms-android';

export interface SMSMessage {
  id: string;
  address: string;
  body: string;
  date: number;
  type: number;
}

export interface ParsedTransaction {
  amount: number;
  type: 'debit' | 'credit';
  account: string;
  merchant?: string;
  date: Date;
  balance?: number;
  smsId: string;
  confidence: number; // 0-1 score for parsing confidence
}

class SMSService {
  private hasPermission = false;

  // Request SMS permission
  async requestSMSPermission(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      console.log('SMS reading is only supported on Android');
      return false;
    }

    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.READ_SMS,
        {
          title: 'SMS Permission',
          message: 'This app needs access to your SMS to automatically track transactions from bank messages.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );

      this.hasPermission = granted === PermissionsAndroid.RESULTS.GRANTED;
      return this.hasPermission;
    } catch (error) {
      console.error('Error requesting SMS permission:', error);
      return false;
    }
  }

  // Check if SMS permission is granted
  async checkSMSPermission(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      const granted = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_SMS);
      this.hasPermission = granted;
      return granted;
    } catch (error) {
      console.error('Error checking SMS permission:', error);
      return false;
    }
  }

  // Read SMS messages from specific senders (banks)
  async readBankSMS(maxCount: number = 100): Promise<SMSMessage[]> {
    if (!this.hasPermission) {
      const permissionGranted = await this.requestSMSPermission();
      if (!permissionGranted) {
        throw new Error('SMS permission not granted');
      }
    }

    return new Promise((resolve, reject) => {
      const filter = {
        box: 'inbox',
        maxCount,
        indexFrom: 0,
      };

      SmsAndroid.list(
        JSON.stringify(filter),
        (fail: any) => {
          console.error('Failed to read SMS:', fail);
          reject(new Error('Failed to read SMS'));
        },
        (count: number, smsList: string) => {
          try {
            const rawMessages = JSON.parse(smsList);
            // Map raw SMS messages to our interface
            const messages: SMSMessage[] = rawMessages.map((msg: any) => ({
              id: msg._id,
              address: msg.address,
              body: msg.body,
              date: msg.date,
              type: msg.type,
            }));
            // Filter for bank SMS messages
            const bankMessages = messages.filter(msg => this.isBankSMS(msg.address, msg.body));
            resolve(bankMessages);
          } catch (error) {
            console.error('Error parsing SMS list:', error);
            reject(error);
          }
        }
      );
    });
  }

  // Check if SMS is from a bank
  private isBankSMS(sender: string, body: string): boolean {
    const bankSenders = [
      'SBIINB', 'HDFCBK', 'ICICIB', 'AXISBK', 'KOTAKBK', 'PNBSMS', 'BOBSMS',
      'CANBNK', 'UNIONBK', 'IOBCHN', 'SBMSMS', 'YESBNK', 'INDBK', 'ALHABK',
      'FEDRAL', 'KRVYBN', 'DCBLTD', 'RBLBNK', 'SCBLTD', 'CITIBK', 'HSBCBK',
      'DEUTSC', 'AMEXIN', 'PAYTM', 'GPAY', 'PHONEPE', 'MOBIKW', 'FREECHARGE'
    ];

    const transactionKeywords = [
      'debited', 'credited', 'withdrawn', 'deposited', 'spent', 'received',
      'payment', 'transaction', 'transfer', 'balance', 'account', 'card',
      'upi', 'neft', 'rtgs', 'imps', 'atm'
    ];

    // Check if sender is a known bank
    const isBankSender = bankSenders.some(bank => 
      sender.toUpperCase().includes(bank) || sender.includes(bank)
    );

    // Check if message contains transaction keywords
    const hasTransactionKeywords = transactionKeywords.some(keyword =>
      body.toLowerCase().includes(keyword)
    );

    return isBankSender || hasTransactionKeywords;
  }

  // Parse transaction from SMS body
  parseTransaction(sms: SMSMessage): ParsedTransaction | null {
    try {
      const body = sms.body.toLowerCase();
      
      // Determine transaction type
      let type: 'debit' | 'credit' = 'debit';
      if (body.includes('credited') || body.includes('deposited') || body.includes('received')) {
        type = 'credit';
      }

      // Extract amount using various patterns
      const amount = this.extractAmount(sms.body);
      if (!amount) {
        return null;
      }

      // Extract account information
      const account = this.extractAccount(sms.body);

      // Extract merchant/description
      const merchant = this.extractMerchant(sms.body);

      // Extract balance
      const balance = this.extractBalance(sms.body);

      // Calculate confidence score
      const confidence = this.calculateConfidence(sms.body, amount, account);

      return {
        amount,
        type,
        account: account || 'Unknown',
        merchant,
        date: new Date(sms.date),
        balance,
        smsId: sms.id,
        confidence,
      };
    } catch (error) {
      console.error('Error parsing transaction:', error);
      return null;
    }
  }

  // Extract amount from SMS body
  private extractAmount(body: string): number | null {
    // Common patterns for amount extraction
    const patterns = [
      /(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.[0-9]{2})?)/i,
      /([0-9,]+(?:\.[0-9]{2})?)\s*(?:rs\.?|inr|₹)/i,
      /(?:amount|amt)\s*(?:rs\.?|inr|₹)?\s*([0-9,]+(?:\.[0-9]{2})?)/i,
      /(?:debited|credited|spent|received)\s*(?:rs\.?|inr|₹)?\s*([0-9,]+(?:\.[0-9]{2})?)/i,
    ];

    for (const pattern of patterns) {
      const match = body.match(pattern);
      if (match) {
        const amountStr = match[1].replace(/,/g, '');
        const amount = parseFloat(amountStr);
        if (!isNaN(amount) && amount > 0) {
          return amount;
        }
      }
    }

    return null;
  }

  // Extract account information
  private extractAccount(body: string): string | null {
    const patterns = [
      /(?:a\/c|account|acc)\s*(?:no\.?|number)?\s*[:\-]?\s*([x*]*[0-9]{4,})/i,
      /(?:card|debit|credit)\s*(?:no\.?|number)?\s*[:\-]?\s*([x*]*[0-9]{4,})/i,
    ];

    for (const pattern of patterns) {
      const match = body.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  // Extract merchant/description
  private extractMerchant(body: string): string | undefined {
    const patterns = [
      /(?:at|to|from)\s+([a-zA-Z0-9\s]+?)(?:\s+on|\s+dated|\s+ref|\s+utr|$)/i,
      /(?:merchant|vendor)\s*[:\-]?\s*([a-zA-Z0-9\s]+?)(?:\s+on|\s+dated|$)/i,
    ];

    for (const pattern of patterns) {
      const match = body.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return undefined;
  }

  // Extract balance
  private extractBalance(body: string): number | undefined {
    const patterns = [
      /(?:balance|bal|available)\s*(?:rs\.?|inr|₹)?\s*([0-9,]+(?:\.[0-9]{2})?)/i,
      /(?:rs\.?|inr|₹)\s*([0-9,]+(?:\.[0-9]{2})?)\s*(?:balance|bal|available)/i,
    ];

    for (const pattern of patterns) {
      const match = body.match(pattern);
      if (match) {
        const balanceStr = match[1].replace(/,/g, '');
        const balance = parseFloat(balanceStr);
        if (!isNaN(balance) && balance >= 0) {
          return balance;
        }
      }
    }

    return undefined;
  }

  // Calculate parsing confidence
  private calculateConfidence(body: string, amount: number, account: string | null): number {
    let confidence = 0.5; // Base confidence

    // Increase confidence based on keywords
    const highConfidenceKeywords = ['debited', 'credited', 'transaction', 'payment'];
    const mediumConfidenceKeywords = ['spent', 'received', 'transfer'];

    for (const keyword of highConfidenceKeywords) {
      if (body.toLowerCase().includes(keyword)) {
        confidence += 0.2;
        break;
      }
    }

    for (const keyword of mediumConfidenceKeywords) {
      if (body.toLowerCase().includes(keyword)) {
        confidence += 0.1;
        break;
      }
    }

    // Increase confidence if account is found
    if (account) {
      confidence += 0.1;
    }

    // Increase confidence if amount format is clear
    if (body.includes('₹') || body.toLowerCase().includes('rs')) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }
}

export const smsService = new SMSService();
