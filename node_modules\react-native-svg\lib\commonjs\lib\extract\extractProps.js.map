{"version": 3, "names": ["_extractFill", "_interopRequireDefault", "require", "_extractStroke", "_extractTransform", "_extractResponder", "_extractOpacity", "_util", "e", "__esModule", "default", "clipRules", "evenodd", "nonzero", "propsAndStyles", "props", "style", "Array", "isArray", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON>", "marker", "undefined", "matched", "match", "idPattern", "extractProps", "ref", "id", "opacity", "onLayout", "clipPath", "clipRule", "display", "mask", "filter", "markerStart", "markerMid", "markerEnd", "testID", "accessibilityLabel", "accessible", "extracted", "inherited", "extractResponder", "extractFill", "extractStroke", "color", "length", "propList", "matrix", "extractTransform", "extractOpacity", "name", "String", "console", "warn", "extract", "instance", "withoutXY", "x", "y"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractProps.ts"], "mappings": ";;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,iBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,iBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,eAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AAAoC,SAAAD,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAYpC,MAAMG,SAA+C,GAAG;EACtDC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AAEM,SAASC,cAAcA,CAACC,KAAwC,EAAE;EACvE,MAAM;IAAEC;EAAM,CAAC,GAAGD,KAAK;EACvB,OAAO,CAACC,KAAK,GACTD,KAAK,GACL;IACE,IAAIE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGJ,KAAK,CAAC,GAAGA,KAAK,CAAC;IAC/D,GAAGD;EACL,CAAC;AACP;AAEA,SAASM,SAASA,CAACC,MAAe,EAAE;EAClC,IAAI,CAACA,MAAM,EAAE;IACX,OAAOC,SAAS;EAClB;EACA,MAAMC,OAAO,GAAGF,MAAM,CAACG,KAAK,CAACC,eAAS,CAAC;EACvC,OAAOF,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGD,SAAS;AACzC;AAEe,SAASI,YAAYA,CAClCZ,KAoBW,EACXa,GAAW,EACX;EACA,MAAM;IACJC,EAAE;IACFC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,OAAO;IACPC,IAAI;IACJC,MAAM;IACNd,MAAM;IACNe,WAAW,GAAGf,MAAM;IACpBgB,SAAS,GAAGhB,MAAM;IAClBiB,SAAS,GAAGjB,MAAM;IAClBkB,MAAM;IACNC,kBAAkB;IAClBC;EACF,CAAC,GAAG3B,KAAK;EACT,MAAM4B,SAAyB,GAAG,CAAC,CAAC;EAEpC,MAAMC,SAAmB,GAAG,EAAE;EAC9B,IAAAC,yBAAgB,EAACF,SAAS,EAAE5B,KAAK,EAAEa,GAAG,CAAC;EACvC,IAAAkB,oBAAW,EAACH,SAAS,EAAE5B,KAAK,EAAE6B,SAAS,CAAC;EACxC,IAAAG,sBAAa,EAACJ,SAAS,EAAE5B,KAAK,EAAE6B,SAAS,CAAC;EAE1C,IAAI7B,KAAK,CAACiC,KAAK,EAAE;IACfL,SAAS,CAACK,KAAK,GAAGjC,KAAK,CAACiC,KAAK;EAC/B;EAEA,IAAIJ,SAAS,CAACK,MAAM,EAAE;IACpBN,SAAS,CAACO,QAAQ,GAAGN,SAAS;EAChC;EAEA,MAAMO,MAAM,GAAG,IAAAC,yBAAgB,EAACrC,KAAK,CAAC;EACtC,IAAIoC,MAAM,KAAK,IAAI,EAAE;IACnBR,SAAS,CAACQ,MAAM,GAAGA,MAAM;EAC3B;EAEA,IAAIrB,OAAO,IAAI,IAAI,EAAE;IACnBa,SAAS,CAACb,OAAO,GAAG,IAAAuB,uBAAc,EAACvB,OAAO,CAAC;EAC7C;EAEA,IAAII,OAAO,IAAI,IAAI,EAAE;IACnBS,SAAS,CAACT,OAAO,GAAGA,OAAO,KAAK,MAAM,GAAG,MAAM,GAAGX,SAAS;EAC7D;EAEA,IAAIQ,QAAQ,EAAE;IACZY,SAAS,CAACZ,QAAQ,GAAGA,QAAQ;EAC/B;EAEA,IAAIM,WAAW,EAAE;IACfM,SAAS,CAACN,WAAW,GAAGhB,SAAS,CAACgB,WAAW,CAAC;EAChD;EACA,IAAIC,SAAS,EAAE;IACbK,SAAS,CAACL,SAAS,GAAGjB,SAAS,CAACiB,SAAS,CAAC;EAC5C;EACA,IAAIC,SAAS,EAAE;IACbI,SAAS,CAACJ,SAAS,GAAGlB,SAAS,CAACkB,SAAS,CAAC;EAC5C;EAEA,IAAIV,EAAE,EAAE;IACNc,SAAS,CAACW,IAAI,GAAGC,MAAM,CAAC1B,EAAE,CAAC;EAC7B;EAEA,IAAIW,MAAM,EAAE;IACVG,SAAS,CAACH,MAAM,GAAGA,MAAM;EAC3B;EAEA,IAAIC,kBAAkB,EAAE;IACtBE,SAAS,CAACF,kBAAkB,GAAGA,kBAAkB;EACnD;EAEA,IAAIC,UAAU,EAAE;IACdC,SAAS,CAACD,UAAU,GAAGA,UAAU;EACnC;EAEA,IAAIT,QAAQ,EAAE;IACZU,SAAS,CAACV,QAAQ,GAAGtB,SAAS,CAACsB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;EACxD;EACA,IAAID,QAAQ,EAAE;IACZ,MAAMR,OAAO,GAAGQ,QAAQ,CAACP,KAAK,CAACC,eAAS,CAAC;IACzC,IAAIF,OAAO,EAAE;MACXmB,SAAS,CAACX,QAAQ,GAAGR,OAAO,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM;MACLgC,OAAO,CAACC,IAAI,CACV,qEAAqE,GACnEzB,QAAQ,GACR,GACJ,CAAC;IACH;EACF;EAEA,IAAIG,IAAI,EAAE;IACR,MAAMX,OAAO,GAAGW,IAAI,CAACV,KAAK,CAACC,eAAS,CAAC;IAErC,IAAIF,OAAO,EAAE;MACXmB,SAAS,CAACR,IAAI,GAAGX,OAAO,CAAC,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLgC,OAAO,CAACC,IAAI,CACV,6DAA6D,GAC3DtB,IAAI,GACJ,GACJ,CAAC;IACH;EACF;EAEA,IAAIC,MAAM,EAAE;IACV,MAAMZ,OAAO,GAAGY,MAAM,CAACX,KAAK,CAACC,eAAS,CAAC;IAEvC,IAAIF,OAAO,EAAE;MACXmB,SAAS,CAACP,MAAM,GAAGZ,OAAO,CAAC,CAAC,CAAC;IAC/B,CAAC,MAAM;MACLgC,OAAO,CAACC,IAAI,CACV,iEAAiE,GAC/DrB,MAAM,GACN,GACJ,CAAC;IACH;EACF;EAEA,OAAOO,SAAS;AAClB;AAEO,SAASe,OAAOA,CACrBC,QAAgB,EAChB5C,KAAwC,EACxC;EACA,OAAOY,YAAY,CAACb,cAAc,CAACC,KAAK,CAAC,EAAE4C,QAAQ,CAAC;AACtD;AAEO,SAASC,SAASA,CACvBD,QAAgB,EAChB5C,KAAwC,EACxC;EACA,OAAOY,YAAY,CAAC;IAAE,GAAGb,cAAc,CAACC,KAAK,CAAC;IAAE8C,CAAC,EAAE,IAAI;IAAEC,CAAC,EAAE;EAAK,CAAC,EAAEH,QAAQ,CAAC;AAC/E", "ignoreList": []}