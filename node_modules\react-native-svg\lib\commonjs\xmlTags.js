"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.tags = void 0;
var _elements = require("./elements");
const tags = exports.tags = {
  circle: _elements.Circle,
  clipPath: _elements.ClipPath,
  defs: _elements.Defs,
  ellipse: _elements.Ellipse,
  filter: _elements.Filter,
  feBlend: _elements.FeBlend,
  feColorMatrix: _elements.FeColorMatrix,
  feComponentTransfer: _elements.FeComponentTransfer,
  feComposite: _elements.FeComposite,
  feConvolveMatrix: _elements.FeConvolveMatrix,
  feDiffuseLighting: _elements.FeDiffuseLighting,
  feDisplacementMap: _elements.FeDisplacementMap,
  feDistantLight: _elements.FeDistantLight,
  feDropShadow: _elements.FeDropShadow,
  feFlood: _elements.FeFlood,
  feGaussianBlur: _elements.FeGaussianBlur,
  feImage: _elements.FeImage,
  feMerge: _elements.FeMerge,
  feMergeNode: _elements.FeMergeNode,
  feMorphology: _elements.FeMorphology,
  feOffset: _elements.FeOffset,
  fePointLight: _elements.FePointLight,
  feSpecularLighting: _elements.FeSpecularLighting,
  feSpotLight: _elements.FeSpotLight,
  feTile: _elements.FeTile,
  feTurbulence: _elements.FeTurbulence,
  foreignObject: _elements.ForeignObject,
  g: _elements.G,
  image: _elements.Image,
  line: _elements.Line,
  linearGradient: _elements.LinearGradient,
  marker: _elements.Marker,
  mask: _elements.Mask,
  path: _elements.Path,
  pattern: _elements.Pattern,
  polygon: _elements.Polygon,
  polyline: _elements.Polyline,
  radialGradient: _elements.RadialGradient,
  rect: _elements.Rect,
  stop: _elements.Stop,
  svg: _elements.Svg,
  symbol: _elements.Symbol,
  text: _elements.Text,
  textPath: _elements.TextPath,
  tspan: _elements.TSpan,
  use: _elements.Use
};
//# sourceMappingURL=xmlTags.js.map