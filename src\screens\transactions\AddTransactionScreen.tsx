// Add Transaction Screen - Income and Expense Management

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import uuid from 'react-native-uuid';
import { COLORS } from '../../constants';
import { useAppStore } from '../../store';
import { transactionService } from '../../services/TransactionService';
import { accountService } from '../../services/AccountService';
import { categoryService } from '../../services/CategoryService';
import { Transaction, Account, Category } from '../../types';

interface AddTransactionScreenProps {
  navigation: any;
  route: {
    params?: {
      type?: 'income' | 'expense';
    };
  };
}

const AddTransactionScreen: React.FC<AddTransactionScreenProps> = ({ navigation, route }) => {
  const { addTransaction, accounts, categories } = useAppStore();
  const [transactionType, setTransactionType] = useState<'income' | 'expense'>(
    route.params?.type || 'expense'
  );
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [selectedAccount, setSelectedAccount] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isRecurring, setIsRecurring] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [availableAccounts, setAvailableAccounts] = useState<Account[]>([]);
  const [availableCategories, setAvailableCategories] = useState<Category[]>([]);

  useEffect(() => {
    loadData();
  }, [transactionType]);

  const loadData = async () => {
    try {
      // Load accounts
      const accountsData = await accountService.getAllAccounts();
      setAvailableAccounts(accountsData);
      if (accountsData.length > 0 && !selectedAccount) {
        setSelectedAccount(accountsData[0].id);
      }

      // Load categories based on transaction type
      const categoriesData = await categoryService.getCategoriesByType(transactionType);
      setAvailableCategories(categoriesData);
      if (categoriesData.length > 0 && !selectedCategory) {
        setSelectedCategory(categoriesData[0].id);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Failed to load data');
    }
  };

  const handleSave = async () => {
    if (!amount || !selectedAccount || !selectedCategory) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    setIsLoading(true);
    try {
      const newTransaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'> = {
        amount: numericAmount,
        type: transactionType,
        categoryId: selectedCategory,
        accountId: selectedAccount,
        paymentMethodId: selectedPaymentMethod || 'cash', // Default to cash if no payment method selected
        description: description.trim(),
        date,
        isRecurring,
        recurringId: undefined,
        smsId: undefined,
        isConfirmed: true,
      };

      const createdTransaction = await transactionService.createTransaction(newTransaction);
      addTransaction(createdTransaction);

      Alert.alert(
        'Success',
        `${transactionType === 'income' ? 'Income' : 'Expense'} added successfully!`,
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error saving transaction:', error);
      Alert.alert('Error', 'Failed to save transaction');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^0-9.]/g, '');
    return numericValue;
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Transaction Type Toggle */}
        <View style={styles.typeToggle}>
          <TouchableOpacity
            style={[
              styles.typeButton,
              transactionType === 'income' && styles.typeButtonActive,
            ]}
            onPress={() => setTransactionType('income')}
          >
            <Ionicons
              name="arrow-down-circle"
              size={20}
              color={transactionType === 'income' ? COLORS.WHITE : COLORS.SUCCESS}
            />
            <Text
              style={[
                styles.typeButtonText,
                transactionType === 'income' && styles.typeButtonTextActive,
              ]}
            >
              Income
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.typeButton,
              transactionType === 'expense' && styles.typeButtonActive,
            ]}
            onPress={() => setTransactionType('expense')}
          >
            <Ionicons
              name="arrow-up-circle"
              size={20}
              color={transactionType === 'expense' ? COLORS.WHITE : COLORS.ERROR}
            />
            <Text
              style={[
                styles.typeButtonText,
                transactionType === 'expense' && styles.typeButtonTextActive,
              ]}
            >
              Expense
            </Text>
          </TouchableOpacity>
        </View>

        {/* Amount Input */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Amount *</Text>
          <View style={styles.amountContainer}>
            <Text style={styles.currencySymbol}>₹</Text>
            <TextInput
              style={styles.amountInput}
              value={amount}
              onChangeText={(text) => setAmount(formatCurrency(text))}
              placeholder="0.00"
              keyboardType="numeric"
              placeholderTextColor={COLORS.TEXT_SECONDARY}
            />
          </View>
        </View>

        {/* Description Input */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={styles.textInput}
            value={description}
            onChangeText={setDescription}
            placeholder={`Enter ${transactionType} description`}
            placeholderTextColor={COLORS.TEXT_SECONDARY}
            multiline
            numberOfLines={2}
          />
        </View>

        {/* Account Selection */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Account *</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={selectedAccount}
              onValueChange={setSelectedAccount}
              style={styles.picker}
            >
              {availableAccounts.map((account) => (
                <Picker.Item
                  key={account.id}
                  label={`${account.name} (₹${account.balance.toLocaleString('en-IN')})`}
                  value={account.id}
                />
              ))}
            </Picker>
          </View>
        </View>

        {/* Category Selection */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Category *</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={selectedCategory}
              onValueChange={setSelectedCategory}
              style={styles.picker}
            >
              {availableCategories.map((category) => (
                <Picker.Item
                  key={category.id}
                  label={category.name}
                  value={category.id}
                />
              ))}
            </Picker>
          </View>
        </View>

        {/* Date Selection */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Date</Text>
          <TouchableOpacity
            style={styles.dateButton}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={styles.dateText}>
              {date.toLocaleDateString('en-IN')}
            </Text>
            <Ionicons name="calendar-outline" size={20} color={COLORS.TEXT_SECONDARY} />
          </TouchableOpacity>
        </View>

        {/* Recurring Toggle */}
        <View style={styles.inputGroup}>
          <View style={styles.switchRow}>
            <Text style={styles.label}>Recurring Transaction</Text>
            <Switch
              value={isRecurring}
              onValueChange={setIsRecurring}
              trackColor={{ false: COLORS.TEXT_SECONDARY, true: COLORS.PRIMARY }}
              thumbColor={isRecurring ? COLORS.WHITE : COLORS.SURFACE}
            />
          </View>
          {isRecurring && (
            <Text style={styles.helperText}>
              This will create a recurring {transactionType} entry
            </Text>
          )}
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={isLoading}
        >
          <Text style={styles.saveButtonText}>
            {isLoading ? 'Saving...' : `Add ${transactionType === 'income' ? 'Income' : 'Expense'}`}
          </Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Date Picker Modal */}
      {showDatePicker && (
        <DateTimePicker
          value={date}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowDatePicker(false);
            if (selectedDate) {
              setDate(selectedDate);
            }
          }}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  typeToggle: {
    flexDirection: 'row',
    marginTop: 16,
    marginBottom: 24,
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: 4,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  typeButtonActive: {
    backgroundColor: COLORS.PRIMARY,
  },
  typeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginLeft: 8,
  },
  typeButtonTextActive: {
    color: COLORS.WHITE,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 8,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  currencySymbol: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    paddingVertical: 12,
  },
  textInput: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    overflow: 'hidden',
  },
  picker: {
    color: COLORS.TEXT_PRIMARY,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  dateText: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  helperText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginTop: 4,
  },
  saveButton: {
    backgroundColor: COLORS.PRIMARY,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 32,
  },
  saveButtonDisabled: {
    backgroundColor: COLORS.TEXT_SECONDARY,
  },
  saveButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
});

export default AddTransactionScreen;
