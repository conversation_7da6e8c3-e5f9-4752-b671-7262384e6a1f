// SMS Sync Service - Automatically sync and create transactions from SMS

import { smsService, ParsedTransaction } from './SMSService';
import { smsParsingEngine } from './SMSParsingEngine';
import { transactionService } from './TransactionService';
import { categoryService } from './CategoryService';
import { accountService } from './AccountService';
import { Transaction, Category, Account } from '../types';
import uuid from 'react-native-uuid';

interface SMSTransaction {
  id: string;
  smsId: string;
  parsedData: ParsedTransaction;
  isConfirmed: boolean;
  createdAt: Date;
}

class SMSSyncService {
  private syncInProgress = false;
  private lastSyncTime: Date | null = null;

  // Sync SMS transactions
  async syncSMSTransactions(): Promise<{
    newTransactions: Transaction[];
    pendingTransactions: SMSTransaction[];
  }> {
    if (this.syncInProgress) {
      throw new Error('Sync already in progress');
    }

    this.syncInProgress = true;
    const newTransactions: Transaction[] = [];
    const pendingTransactions: SMSTransaction[] = [];

    try {
      // Check SMS permission
      const hasPermission = await smsService.checkSMSPermission();
      if (!hasPermission) {
        const granted = await smsService.requestSMSPermission();
        if (!granted) {
          throw new Error('SMS permission required for automatic transaction sync');
        }
      }

      // Read recent SMS messages
      const smsMessages = await smsService.readBankSMS(50);
      console.log(`Found ${smsMessages.length} bank SMS messages`);

      // Get existing SMS IDs to avoid duplicates
      const existingSMSIds = await this.getExistingSMSIds();

      for (const sms of smsMessages) {
        // Skip if already processed
        if (existingSMSIds.includes(sms.id)) {
          continue;
        }

        // Parse transaction from SMS
        const parsedTransaction = smsParsingEngine.parseTransactionAdvanced(sms);
        if (!parsedTransaction) {
          continue;
        }

        // Create SMS transaction record
        const smsTransaction: SMSTransaction = {
          id: uuid.v4() as string,
          smsId: sms.id,
          parsedData: parsedTransaction,
          isConfirmed: parsedTransaction.confidence > 0.8, // Auto-confirm high confidence
          createdAt: new Date(),
        };

        if (smsTransaction.isConfirmed) {
          // Auto-create transaction for high confidence
          try {
            const transaction = await this.createTransactionFromSMS(smsTransaction);
            if (transaction) {
              newTransactions.push(transaction);
            }
          } catch (error) {
            console.error('Error creating transaction from SMS:', error);
            // Add to pending if auto-creation fails
            pendingTransactions.push(smsTransaction);
          }
        } else {
          // Add to pending for manual review
          pendingTransactions.push(smsTransaction);
        }
      }

      this.lastSyncTime = new Date();
      console.log(`SMS sync completed: ${newTransactions.length} new, ${pendingTransactions.length} pending`);

      return { newTransactions, pendingTransactions };
    } finally {
      this.syncInProgress = false;
    }
  }

  // Create transaction from SMS data
  private async createTransactionFromSMS(smsTransaction: SMSTransaction): Promise<Transaction | null> {
    const { parsedData } = smsTransaction;

    try {
      // Find or create account
      const account = await this.findOrCreateAccount(parsedData.account);
      if (!account) {
        throw new Error('Could not find or create account');
      }

      // Find appropriate category
      const category = await this.findAppropriateCategory(parsedData);
      if (!category) {
        throw new Error('Could not find appropriate category');
      }

      // Create transaction
      const transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'> = {
        amount: parsedData.amount,
        type: parsedData.type === 'credit' ? 'income' : 'expense',
        categoryId: category.id,
        accountId: account.id,
        paymentMethodId: 'sms', // Default payment method for SMS transactions
        description: parsedData.merchant || 'SMS Transaction',
        date: parsedData.date,
        isRecurring: false,
        recurringId: undefined,
        smsId: parsedData.smsId,
        isConfirmed: true,
      };

      const transaction = await transactionService.createTransaction(transactionData);
      console.log(`Created transaction from SMS: ${transaction.id}`);
      
      return transaction;
    } catch (error) {
      console.error('Error creating transaction from SMS:', error);
      return null;
    }
  }

  // Find or create account based on SMS data
  private async findOrCreateAccount(accountInfo: string): Promise<Account | null> {
    try {
      // Get all accounts
      const accounts = await accountService.getAllAccounts();

      // Try to find existing account by partial match
      const existingAccount = accounts.find(acc => 
        acc.name.toLowerCase().includes(accountInfo.toLowerCase()) ||
        acc.accountNumber?.includes(accountInfo.slice(-4)) // Match last 4 digits
      );

      if (existingAccount) {
        return existingAccount;
      }

      // Create new account if not found
      const newAccount: Omit<Account, 'id' | 'createdAt' | 'updatedAt'> = {
        name: `Account ${accountInfo}`,
        type: 'savings', // Default type
        balance: 0,
        currency: 'INR',
        accountNumber: accountInfo,
        bankName: undefined,
        isActive: true,
      };

      return await accountService.createAccount(newAccount);
    } catch (error) {
      console.error('Error finding/creating account:', error);
      return null;
    }
  }

  // Find appropriate category based on transaction data
  private async findAppropriateCategory(parsedData: ParsedTransaction): Promise<Category | null> {
    try {
      const transactionType = parsedData.type === 'credit' ? 'income' : 'expense';
      const categories = await categoryService.getCategoriesByType(transactionType);

      if (categories.length === 0) {
        return null;
      }

      // Try to match category based on merchant
      if (parsedData.merchant) {
        const merchant = parsedData.merchant.toLowerCase();
        
        // Define category keywords
        const categoryKeywords = {
          'food': ['restaurant', 'cafe', 'food', 'zomato', 'swiggy', 'dominos', 'mcdonald'],
          'transport': ['uber', 'ola', 'metro', 'bus', 'taxi', 'petrol', 'fuel'],
          'shopping': ['amazon', 'flipkart', 'mall', 'store', 'shop', 'market'],
          'entertainment': ['movie', 'cinema', 'netflix', 'spotify', 'game'],
          'utilities': ['electricity', 'water', 'gas', 'internet', 'mobile', 'recharge'],
          'healthcare': ['hospital', 'clinic', 'pharmacy', 'medical', 'doctor'],
        };

        for (const [categoryType, keywords] of Object.entries(categoryKeywords)) {
          if (keywords.some(keyword => merchant.includes(keyword))) {
            const matchingCategory = categories.find(cat => 
              cat.name.toLowerCase().includes(categoryType)
            );
            if (matchingCategory) {
              return matchingCategory;
            }
          }
        }
      }

      // Return default category for the transaction type
      if (transactionType === 'income') {
        return categories.find(cat => cat.name.toLowerCase().includes('salary')) || categories[0];
      } else {
        return categories.find(cat => cat.name.toLowerCase().includes('general')) || categories[0];
      }
    } catch (error) {
      console.error('Error finding appropriate category:', error);
      return null;
    }
  }

  // Get existing SMS IDs to avoid duplicates
  private async getExistingSMSIds(): Promise<string[]> {
    try {
      const transactions = await transactionService.getAllTransactions();
      return transactions
        .filter(t => t.smsId)
        .map(t => t.smsId!);
    } catch (error) {
      console.error('Error getting existing SMS IDs:', error);
      return [];
    }
  }

  // Get last sync time
  getLastSyncTime(): Date | null {
    return this.lastSyncTime;
  }

  // Check if sync is in progress
  isSyncInProgress(): boolean {
    return this.syncInProgress;
  }

  // Manual confirmation of pending SMS transaction
  async confirmSMSTransaction(smsTransaction: SMSTransaction): Promise<Transaction | null> {
    try {
      const transaction = await this.createTransactionFromSMS(smsTransaction);
      if (transaction) {
        console.log(`Manually confirmed SMS transaction: ${transaction.id}`);
      }
      return transaction;
    } catch (error) {
      console.error('Error confirming SMS transaction:', error);
      return null;
    }
  }

  // Reject SMS transaction
  async rejectSMSTransaction(smsTransaction: SMSTransaction): Promise<void> {
    // In a real app, you might want to store rejected SMS IDs to avoid re-processing
    console.log(`Rejected SMS transaction: ${smsTransaction.id}`);
  }
}

export const smsSyncService = new SMSSyncService();
